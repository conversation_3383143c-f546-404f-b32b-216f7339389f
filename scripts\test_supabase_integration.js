#!/usr/bin/env node

/**
 * Test script to validate Supabase integration fixes
 * Tests connection, authentication, and data operations
 */

const fs = require('fs');
const path = require('path');

class SupabaseIntegrationTester {
  constructor() {
    this.testResults = {
      connectionTest: { status: 'PENDING', details: {} },
      authContextTest: { status: 'PENDING', details: {} },
      serviceMethodsTest: { status: 'PENDING', details: {} },
      pageLoadingTest: { status: 'PENDING', details: {} },
      errorHandlingTest: { status: 'PENDING', details: {} }
    };
  }

  /**
   * Test Supabase connection and configuration
   */
  testConnection() {
    console.log('🔗 Testing Supabase connection configuration...');
    
    const issues = [];
    const details = {};

    // Check .env file
    const envPath = path.join(__dirname, '..', '.env');
    if (!fs.existsSync(envPath)) {
      issues.push('.env file not found');
    } else {
      const envContent = fs.readFileSync(envPath, 'utf8');
      details.hasSupabaseUrl = envContent.includes('EXPO_PUBLIC_SUPABASE_URL=');
      details.hasSupabaseKey = envContent.includes('EXPO_PUBLIC_SUPABASE_ANON_KEY=');
      details.hasCorrectAnonKey = envContent.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqeWZ5ZHNpdmhtcHFqa25xd3p2Iiwicm9sZSI6ImFub24i');
      details.noServiceRoleKey = !envContent.includes('InNlcnZpY2Vfcm9sZSI');
      details.aiConfigRemoved = envContent.includes('# EXPO_PUBLIC_AI_API_URL=');
      details.sentryConfigRemoved = envContent.includes('# EXPO_PUBLIC_SENTRY_DSN=');

      if (!details.hasSupabaseUrl) issues.push('EXPO_PUBLIC_SUPABASE_URL missing');
      if (!details.hasSupabaseKey) issues.push('EXPO_PUBLIC_SUPABASE_ANON_KEY missing');
      if (!details.hasCorrectAnonKey) issues.push('Using incorrect Supabase key (should be anon key)');
      if (!details.noServiceRoleKey) issues.push('Still using service role key instead of anon key');
    }

    // Check lib/supabase.ts
    const supabasePath = path.join(__dirname, '..', 'lib', 'supabase.ts');
    if (fs.existsSync(supabasePath)) {
      const supabaseContent = fs.readFileSync(supabasePath, 'utf8');
      details.hasCorrectFallback = supabaseContent.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqeWZ5ZHNpdmhtcHFqa25xd3p2Iiwicm9sZSI6ImFub24i');
      details.hasStorageAdapter = supabaseContent.includes('createStorageAdapter');
      details.hasProperConfig = supabaseContent.includes('autoRefreshToken: true');

      if (!details.hasCorrectFallback) issues.push('lib/supabase.ts has incorrect fallback key');
      if (!details.hasStorageAdapter) issues.push('Missing storage adapter configuration');
      if (!details.hasProperConfig) issues.push('Missing proper Supabase client configuration');
    }

    this.testResults.connectionTest = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };
  }

  /**
   * Test AuthContext improvements
   */
  testAuthContext() {
    console.log('🔐 Testing AuthContext improvements...');
    
    const issues = [];
    const details = {};

    const authContextPath = path.join(__dirname, '..', 'contexts', 'AuthContext.tsx');
    if (!fs.existsSync(authContextPath)) {
      issues.push('AuthContext.tsx not found');
    } else {
      const authContent = fs.readFileSync(authContextPath, 'utf8');
      
      details.hasTimeoutHandling = authContent.includes('timeout');
      details.hasProperErrorHandling = authContent.includes('catch (error)');
      details.hasCircularDependencyFix = authContent.includes('}, []); // Remove user dependency');
      details.hasConnectionTesting = authContent.includes('Promise.race');
      details.hasLoadingStateManagement = authContent.includes('isMounted');
      details.hasBasicProfileFallback = authContent.includes('setProfile({');

      if (!details.hasTimeoutHandling) issues.push('Missing timeout handling in AuthContext');
      if (!details.hasProperErrorHandling) issues.push('Insufficient error handling in AuthContext');
      if (!details.hasCircularDependencyFix) issues.push('Circular dependency not fixed in fetchProfile');
      if (!details.hasConnectionTesting) issues.push('Missing connection testing in auth operations');
      if (!details.hasLoadingStateManagement) issues.push('Missing proper loading state management');
      if (!details.hasBasicProfileFallback) issues.push('Missing basic profile fallback on errors');
    }

    this.testResults.authContextTest = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };
  }

  /**
   * Test SupabaseService improvements
   */
  testServiceMethods() {
    console.log('🛠️ Testing SupabaseService improvements...');
    
    const issues = [];
    const details = {};

    const servicePath = path.join(__dirname, '..', 'services', 'supabaseService.ts');
    if (!fs.existsSync(servicePath)) {
      issues.push('supabaseService.ts not found');
    } else {
      const serviceContent = fs.readFileSync(servicePath, 'utf8');
      
      details.hasConnectionTest = serviceContent.includes('testConnection()');
      details.hasTimeoutHandling = serviceContent.includes('timeoutPromise');
      details.hasRetryLogic = serviceContent.includes('withRetry');
      details.hasProperErrorHandling = serviceContent.includes('SupabaseErrorHandler');
      details.hasNetworkErrorDetection = serviceContent.includes('isNetworkError');
      details.hasUserStatsTimeout = serviceContent.includes('Stats query timeout');
      details.hasMeasurementsTimeout = serviceContent.includes('Query timeout');

      if (!details.hasConnectionTest) issues.push('Missing connection test method');
      if (!details.hasTimeoutHandling) issues.push('Missing timeout handling in service methods');
      if (!details.hasRetryLogic) issues.push('Missing retry logic for failed operations');
      if (!details.hasProperErrorHandling) issues.push('Missing proper error handling');
      if (!details.hasNetworkErrorDetection) issues.push('Missing network error detection');
      if (!details.hasUserStatsTimeout) issues.push('Missing timeout handling in getUserStats');
      if (!details.hasMeasurementsTimeout) issues.push('Missing timeout handling in getMeasurements');
    }

    this.testResults.serviceMethodsTest = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };
  }

  /**
   * Test page loading improvements
   */
  testPageLoading() {
    console.log('📄 Testing page loading improvements...');
    
    const issues = [];
    const details = {};

    // Test account page
    const accountPath = path.join(__dirname, '..', 'app', '(tabs)', 'account.tsx');
    if (fs.existsSync(accountPath)) {
      const accountContent = fs.readFileSync(accountPath, 'utf8');
      details.accountHasUseCallback = accountContent.includes('useCallback');
      details.accountHasProperLoading = accountContent.includes('authLoading');
      details.accountHasErrorHandling = accountContent.includes('statsError');
      details.accountHasRetryButton = accountContent.includes('loadUserStats()');

      if (!details.accountHasUseCallback) issues.push('Account page missing useCallback optimization');
      if (!details.accountHasProperLoading) issues.push('Account page missing proper loading state management');
      if (!details.accountHasErrorHandling) issues.push('Account page missing error handling');
      if (!details.accountHasRetryButton) issues.push('Account page retry button not using loadUserStats');
    }

    // Test history page
    const historyPath = path.join(__dirname, '..', 'app', '(tabs)', 'history.tsx');
    if (fs.existsSync(historyPath)) {
      const historyContent = fs.readFileSync(historyPath, 'utf8');
      details.historyHasProperLoading = historyContent.includes('setLoading(true)');
      details.historyHasErrorHandling = historyContent.includes('timeout');
      details.historyHasUserCheck = historyContent.includes('&& user');
      details.historyHasLogoutHandling = historyContent.includes('setMeasurements([])');

      if (!details.historyHasProperLoading) issues.push('History page missing proper loading state');
      if (!details.historyHasErrorHandling) issues.push('History page missing timeout error handling');
      if (!details.historyHasUserCheck) issues.push('History page missing user check in useEffect');
      if (!details.historyHasLogoutHandling) issues.push('History page missing logout data clearing');
    }

    this.testResults.pageLoadingTest = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };
  }

  /**
   * Test error handling and offline behavior
   */
  testErrorHandling() {
    console.log('⚠️ Testing error handling and offline behavior...');
    
    const issues = [];
    const details = {};

    // Check if proper error messages are implemented
    const servicePath = path.join(__dirname, '..', 'services', 'supabaseService.ts');
    if (fs.existsSync(servicePath)) {
      const serviceContent = fs.readFileSync(servicePath, 'utf8');
      details.hasOfflineDetection = serviceContent.includes('No internet connection');
      details.hasTimeoutMessages = serviceContent.includes('Request timed out');
      details.hasConnectionTestError = serviceContent.includes('Connection test timeout');
      details.hasProperErrorReturns = serviceContent.includes('{ data: [], error:');

      if (!details.hasOfflineDetection) issues.push('Missing offline detection messages');
      if (!details.hasTimeoutMessages) issues.push('Missing timeout error messages');
      if (!details.hasConnectionTestError) issues.push('Missing connection test error handling');
      if (!details.hasProperErrorReturns) issues.push('Missing proper error return structures');
    }

    // Check if pages handle errors properly
    const accountPath = path.join(__dirname, '..', 'app', '(tabs)', 'account.tsx');
    if (fs.existsSync(accountPath)) {
      const accountContent = fs.readFileSync(accountPath, 'utf8');
      details.accountHasInternetMessage = accountContent.includes('internet connection');
      
      if (!details.accountHasInternetMessage) issues.push('Account page missing internet connection error message');
    }

    this.testResults.errorHandlingTest = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };
  }

  /**
   * Run all tests
   */
  runAllTests() {
    console.log('🧪 Running Supabase Integration Tests...\n');
    
    this.testConnection();
    this.testAuthContext();
    this.testServiceMethods();
    this.testPageLoading();
    this.testErrorHandling();
    
    this.generateReport();
  }

  /**
   * Generate test report
   */
  generateReport() {
    console.log('\n📊 SUPABASE INTEGRATION TEST REPORT');
    console.log('=====================================\n');

    const allTests = Object.keys(this.testResults);
    const passedTests = allTests.filter(test => this.testResults[test].status === 'PASS');
    const failedTests = allTests.filter(test => this.testResults[test].status === 'FAIL');

    console.log(`✅ Passed: ${passedTests.length}/${allTests.length}`);
    console.log(`❌ Failed: ${failedTests.length}/${allTests.length}\n`);

    // Detailed results
    allTests.forEach(testName => {
      const result = this.testResults[testName];
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${testName.toUpperCase()}`);
      
      if (result.issues && result.issues.length > 0) {
        result.issues.forEach(issue => {
          console.log(`   - ${issue}`);
        });
      }
      console.log('');
    });

    // Overall assessment
    if (failedTests.length === 0) {
      console.log('🎉 ALL TESTS PASSED! Supabase integration is ready for academic demonstrations.');
    } else {
      console.log('⚠️  Some tests failed. Please address the issues above before academic demonstrations.');
    }
  }
}

// Run the tests
const tester = new SupabaseIntegrationTester();
tester.runAllTests();
