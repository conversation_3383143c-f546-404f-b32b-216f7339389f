import { log } from '@/utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

// Simplified storage adapter for React Native
const createStorageAdapter = () => {
  return {
    getItem: async (key: string) => {
      try {
        return await AsyncStorage.getItem(key);
      } catch (error) {
        log.warn('Storage getItem error', 'Supabase', error);
        return null;
      }
    },
    setItem: async (key: string, value: string) => {
      try {
        await AsyncStorage.setItem(key, value);
      } catch (error) {
        log.warn('Storage setItem error', 'Supabase', error);
      }
    },
    removeItem: async (key: string) => {
      try {
        await AsyncStorage.removeItem(key);
      } catch (error) {
        log.warn('Storage removeItem error', 'Supabase', error);
      }
    },
  };
};

// Supabase configuration with fallback values for academic demo
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://ajyfydsivhmpqjknqwzv.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqeWZ5ZHNpdmhtcHFqa25xd3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMTgxODUsImV4cCI6MjA2Njc5NDE4NX0.wMJmybTd8w2XNAS6khCE7UWR32fHLU3__vpGtKAJHm0';

// Validate configuration
if (!supabaseUrl || !supabaseAnonKey) {
  log.error('Supabase configuration missing', 'Supabase', {
    hasUrl: !!supabaseUrl,
    hasKey: !!supabaseAnonKey,
  });
  throw new Error(
    'Missing Supabase environment variables. Please check your .env file and ensure EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY are set.'
  );
}

log.info('Supabase client configured successfully', 'Supabase', {
  url: supabaseUrl.substring(0, 30) + '...',
  hasKey: !!supabaseAnonKey,
});

// Create Supabase client with cross-platform storage adapter
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: createStorageAdapter(),
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          preferred_brands: string[] | null;
          preferred_unit: 'cm' | 'inches';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          preferred_brands?: string[] | null;
          preferred_unit?: 'cm' | 'inches';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          preferred_brands?: string[] | null;
          preferred_unit?: 'cm' | 'inches';
          created_at?: string;
          updated_at?: string;
        };
      };
      measurements: {
        Row: {
          id: string;
          user_id: string;
          image_url: string;
          foot_length: number;
          foot_width: number;
          recommended_size_uk: string;
          recommended_size_us: string;
          recommended_size_eu: string;
          confidence: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          image_url: string;
          foot_length: number;
          foot_width: number;
          recommended_size_uk: string;
          recommended_size_us: string;
          recommended_size_eu: string;
          confidence: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          image_url?: string;
          foot_length?: number;
          foot_width?: number;
          recommended_size_uk?: string;
          recommended_size_us?: string;
          recommended_size_eu?: string;
          confidence?: number;
          created_at?: string;
        };
      };
      shoe_recommendations: {
        Row: {
          id: string;
          measurement_id: string;
          brand: string;
          model: string;
          size_uk: string;
          size_us: string;
          size_eu: string;
          confidence: number;
          fit_type: 'narrow' | 'regular' | 'wide';
          category: string;
          image_url: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          measurement_id: string;
          brand: string;
          model: string;
          size_uk: string;
          size_us: string;
          size_eu: string;
          confidence: number;
          fit_type: 'narrow' | 'regular' | 'wide';
          category: string;
          image_url?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          measurement_id?: string;
          brand?: string;
          model?: string;
          size_uk?: string;
          size_us?: string;
          size_eu?: string;
          confidence?: number;
          fit_type?: 'narrow' | 'regular' | 'wide';
          category?: string;
          image_url?: string | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
