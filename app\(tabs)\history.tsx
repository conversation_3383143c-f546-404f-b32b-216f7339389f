import * as Haptics from 'expo-haptics';
import { useCallback, useEffect, useState } from 'react';
import { Alert, RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Dropdown, DropdownOption } from '@/components/ui';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/Loading';
import { useBottomTabOverflow } from '@/components/ui/TabBarBackground';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';

// Import router for navigation
const { router } = require('expo-router');

export default function HistoryScreen() {
  const { colors } = useTheme();
  const { user, loading: authLoading } = useAuth();
  const bottom = useBottomTabOverflow();
  const [measurements, setMeasurements] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filtering state
  const [filterBrand, setFilterBrand] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [loadingFavorites, setLoadingFavorites] = useState(true);
  const [deletingMeasurements, setDeletingMeasurements] = useState<Set<string>>(new Set());

  // Dynamic filter options
  const [brandOptions, setBrandOptions] = useState<DropdownOption[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<DropdownOption[]>([]);

  // Network connectivity check
  const checkNetworkConnectivity = async (): Promise<boolean> => {
    try {
      // Simple connectivity check by trying to reach Supabase
      const { supabase } = await import('@/lib/supabase');
      const { error } = await supabase.from('profiles').select('id').limit(1);
      return !error;
    } catch {
      return false;
    }
  };

  // Navigate to measurement details
  const handleViewMeasurementDetails = async (measurement: any) => {
    try {
      // Provide haptic feedback
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Validate measurement data
      if (!measurement || !measurement.id) {
        Alert.alert(
          'Error',
          'Unable to view measurement details. The measurement data appears to be incomplete.',
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }

      // Prepare measurement data in the format expected by results page
      const measurementData = {
        foot_length: measurement.foot_length,
        foot_width: measurement.foot_width,
        recommended_size_uk: measurement.recommended_size_uk,
        recommended_size_us: measurement.recommended_size_us,
        recommended_size_eu: measurement.recommended_size_eu,
        confidence: measurement.confidence,
        recommendations: measurement.recommendations || []
      };

      // Navigate to results page with measurement data
      router.push({
        pathname: '/results',
        params: {
          measurementId: measurement.id,
          measurementData: JSON.stringify(measurementData),
          imageUri: measurement.image_url || '',
          fromHistory: 'true'
        }
      });
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Error navigating to measurement details', 'HistoryScreen', error);

      Alert.alert(
        'Navigation Error',
        'Unable to view measurement details. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  // Load dynamic filter options
  const loadFilterOptions = useCallback(async () => {
    try {
      // Import functions dynamically to avoid import issues
      const { getShoeBrands, getShoeCategories } = await import('@/services/shoeDataService');

      const [brands, categories] = await Promise.all([
        getShoeBrands(),
        getShoeCategories(),
      ]);

      // Convert brands to dropdown options
      const brandOpts: DropdownOption[] = [
        { value: 'all', label: 'All Brands', icon: 'building.2' },
        ...brands.map(brand => ({
          value: brand.name.toLowerCase(),
          label: brand.name,
          icon: 'building.2'
        }))
      ];

      // Convert categories to dropdown options
      const categoryOpts: DropdownOption[] = [
        { value: 'all', label: 'All Categories', icon: 'grid' },
        ...categories.map(category => ({
          value: category.name.toLowerCase(),
          label: category.name,
          icon: 'grid'
        }))
      ];

      setBrandOptions(brandOpts);
      setCategoryOptions(categoryOpts);
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Error loading filter options', 'HistoryScreen', error);
      // Fallback to hardcoded options
      setBrandOptions([
        { value: 'all', label: 'All Brands', icon: 'building.2' },
        { value: 'nike', label: 'Nike', icon: 'building.2' },
        { value: 'adidas', label: 'Adidas', icon: 'building.2' },
        { value: 'converse', label: 'Converse', icon: 'building.2' },
        { value: 'vans', label: 'Vans', icon: 'building.2' },
      ]);
      setCategoryOptions([
        { value: 'all', label: 'All Categories', icon: 'grid' },
        { value: 'performance sports', label: 'Performance Sports', icon: 'grid' },
        { value: 'everyday casual', label: 'Everyday Casual', icon: 'grid' },
        { value: 'outdoor & hiking', label: 'Outdoor & Hiking', icon: 'grid' },
      ]);
    }
  }, []);

  // Load favorites from AsyncStorage
  const loadFavorites = useCallback(async () => {
    if (!user?.id) {
      setLoadingFavorites(false);
      return;
    }

    try {
      setLoadingFavorites(true);
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      const favoritesJson = await AsyncStorage.default.getItem(`favorites_${user.id}`);

      if (favoritesJson) {
        const favoritesArray = JSON.parse(favoritesJson);
        if (Array.isArray(favoritesArray)) {
          setFavorites(new Set(favoritesArray));
          const { log } = await import('@/utils/logger');
          log.debug(`Loaded ${favoritesArray.length} favorites from storage`, 'HistoryScreen');
        }
      } else {
        const { log } = await import('@/utils/logger');
        log.debug('No favorites found in storage, starting fresh', 'HistoryScreen');
        setFavorites(new Set());
      }
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Error loading favorites', 'HistoryScreen', error);
      setFavorites(new Set()); // Fallback to empty set
    } finally {
      setLoadingFavorites(false);
    }
  }, [user?.id]);

  // Save favorites to AsyncStorage
  const saveFavorites = async (newFavorites: Set<string>) => {
    if (!user?.id) return;

    try {
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      const favoritesArray = Array.from(newFavorites);
      await AsyncStorage.default.setItem(`favorites_${user.id}`, JSON.stringify(favoritesArray));
      const { log } = await import('@/utils/logger');
      log.debug(`Saved ${favoritesArray.length} favorites to storage`, 'HistoryScreen');
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Error saving favorites', 'HistoryScreen', error);
      // Show user-friendly error
      Alert.alert(
        'Save Error',
        'Failed to save favorite status. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Load measurements from Supabase with better error handling
  const loadMeasurements = useCallback(async () => {
    if (!user) {
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { SupabaseService } = await import('@/services/supabaseService');
      const { data, error } = await SupabaseService.getMeasurements(user.id);

      if (error) {
        setError(`Failed to load measurements: ${error}`);
        setMeasurements([]);
        return;
      }

      setMeasurements(data);
      setError(null);
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Error loading measurements', 'HistoryScreen', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('timeout')) {
        setError('Request timed out. Please check your internet connection and try again.');
      } else {
        setError('Unable to load measurements. Please check your internet connection and try again.');
      }
      setMeasurements([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user]);

  useEffect(() => {
    if (!authLoading && user) {
      loadMeasurements();
      loadFilterOptions();
      loadFavorites();
    } else if (!authLoading && !user) {
      // Clear data when user logs out
      setMeasurements([]);
      setError(null);
      setLoading(false);
    }
  }, [user, authLoading, loadMeasurements, loadFilterOptions, loadFavorites]);

  const handleRefresh = () => {
    setRefreshing(true);
    loadMeasurements();
  };

  // Delete measurement with enhanced error handling and user feedback
  const handleDeleteMeasurement = async (measurementId: string) => {
    // Add to deleting set to show loading state
    setDeletingMeasurements(prev => new Set(prev).add(measurementId));

    try {
      // Check network connectivity first
      const isConnected = await checkNetworkConnectivity();
      if (!isConnected) {
        Alert.alert(
          'No Internet Connection',
          'Please check your internet connection and try again.',
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }

      const { SupabaseService } = await import('@/services/supabaseService');
      const { error } = await SupabaseService.deleteMeasurement(measurementId);

      if (error) {
        const { log } = await import('@/utils/logger');
        log.error('Failed to delete measurement', 'HistoryScreen', error);

        // Provide specific error messages based on error type
        let errorMessage = 'Failed to delete measurement. Please try again.';
        if (error.includes('network') || error.includes('timeout')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.includes('permission') || error.includes('unauthorized')) {
          errorMessage = 'You don\'t have permission to delete this measurement.';
        } else if (error.includes('not found')) {
          errorMessage = 'This measurement has already been deleted.';
        }

        Alert.alert('Delete Failed', errorMessage, [
          { text: 'OK', style: 'default' }
        ]);
        return;
      }

      // Remove from local state immediately for responsive UI
      setMeasurements(prev => prev.filter(m => m.id !== measurementId));

      // Also remove from favorites if it was favorited
      setFavorites(prev => {
        const newFavorites = new Set(prev);
        newFavorites.delete(measurementId);
        // Save updated favorites to storage
        saveFavorites(newFavorites);
        return newFavorites;
      });

      // Provide success feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Optional: Show success message (can be removed if too intrusive)
      // Alert.alert('Success', 'Measurement deleted successfully.', [
      //   { text: 'OK', style: 'default' }
      // ]);

    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Error deleting measurement', 'HistoryScreen', error);

      Alert.alert(
        'Unexpected Error',
        'An unexpected error occurred while deleting the measurement. Please try again later.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      // Remove from deleting set
      setDeletingMeasurements(prev => {
        const newSet = new Set(prev);
        newSet.delete(measurementId);
        return newSet;
      });
    }
  };

  // Confirm delete with enhanced messaging
  const confirmDelete = useCallback((measurementId: string, date: string) => {
    // Check if measurement is currently being deleted
    if (deletingMeasurements.has(measurementId)) {
      return; // Prevent multiple delete attempts
    }

    Alert.alert(
      'Delete Measurement',
      `Are you sure you want to delete the measurement from ${date}?\n\nThis action cannot be undone and will also remove any associated images.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {
            // Provide haptic feedback for cancel
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Provide haptic feedback for destructive action
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            handleDeleteMeasurement(measurementId);
          }
        }
      ]
    );
  }, [deletingMeasurements]);

  // Toggle favorite
  const toggleFavorite = async (measurementId: string) => {
    const newFavorites = new Set(favorites);
    const wasAdded = !newFavorites.has(measurementId);

    if (newFavorites.has(measurementId)) {
      newFavorites.delete(measurementId);
    } else {
      newFavorites.add(measurementId);
    }

    // Update UI immediately for responsiveness
    setFavorites(newFavorites);

    // Provide haptic feedback
    Haptics.impactAsync(
      wasAdded ? Haptics.ImpactFeedbackStyle.Medium : Haptics.ImpactFeedbackStyle.Light
    );

    // Save to storage
    await saveFavorites(newFavorites);
  };



  // Filter measurements
  const getFilteredMeasurements = () => {
    let filtered = [...measurements];

    // Filter by favorites
    if (showFavoritesOnly) {
      filtered = filtered.filter(m => favorites.has(m.id));
    }

    // Filter by brand
    if (filterBrand !== 'all') {
      filtered = filtered.filter(m =>
        m.recommendations?.some((rec: any) =>
          rec.brand?.toLowerCase() === filterBrand.toLowerCase()
        )
      );
    }

    // Filter by category
    if (filterCategory !== 'all') {
      filtered = filtered.filter(m =>
        m.recommendations?.some((rec: any) =>
          rec.category?.toLowerCase() === filterCategory.toLowerCase()
        )
      );
    }

    return filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  };

  const renderMeasurementCard = (measurement: any) => {
    const isFavorite = favorites.has(measurement.id);
    const isDeleting = deletingMeasurements.has(measurement.id);
    const measurementDate = new Date(measurement.created_at).toLocaleDateString();

    return (
      <TouchableOpacity
        key={measurement.id}
        style={[
          styles.cardContainer,
          {
            opacity: isDeleting ? 0.6 : 1.0
          }
        ]}
        onPress={() => handleViewMeasurementDetails(measurement)}
        activeOpacity={0.7}
        disabled={isDeleting}
      >
        <ThemedView
          style={[
            styles.card,
            {
              borderColor: colors.border,
            }
          ]}
        >
        <View style={styles.cardHeader}>
          <View style={styles.cardHeaderLeft}>
            <View style={styles.cardHeaderInfo}>
              <ThemedText variant="labelLarge">
                {measurementDate}
              </ThemedText>
              <ThemedText variant="h4" color="primary">
                UK {measurement.recommended_size_uk}
              </ThemedText>
            </View>
            <View style={styles.tapHint}>
              <IconSymbol
                size={16}
                name="chevron.right"
                color={colors.iconSecondary}
              />
            </View>
          </View>

          <View style={styles.cardActions}>
            {/* Favorite button */}
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: colors.backgroundSecondary }
              ]}
              onPress={(e) => {
                e.stopPropagation();
                toggleFavorite(measurement.id);
              }}
              activeOpacity={0.7}
              disabled={isDeleting}
            >
              <IconSymbol
                size={20}
                name={isFavorite ? "heart.fill" : "heart"}
                color={isFavorite ? colors.primary : colors.iconSecondary}
              />
            </TouchableOpacity>

            {/* Delete button with loading state */}
            <TouchableOpacity
              style={[
                styles.actionButton,
                {
                  backgroundColor: isDeleting ? colors.primary + '20' : colors.backgroundSecondary
                }
              ]}
              onPress={(e) => {
                e.stopPropagation();
                confirmDelete(measurement.id, measurementDate);
              }}
              activeOpacity={0.7}
              disabled={isDeleting}
            >
              <IconSymbol
                size={20}
                name={isDeleting ? "hourglass" : "trash"}
                color={isDeleting ? colors.primary : colors.iconSecondary}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.measurements}>
          <View style={styles.measurementItem}>
            <ThemedText variant="caption" color="secondary">Length</ThemedText>
            <ThemedText variant="mono">{measurement.foot_length} cm</ThemedText>
          </View>
          <View style={styles.measurementItem}>
            <ThemedText variant="caption" color="secondary">Width</ThemedText>
            <ThemedText variant="mono">{measurement.foot_width} cm</ThemedText>
          </View>
        </View>

        <View style={styles.recommendations}>
          <ThemedText variant="caption" color="secondary">Recommended Shoes</ThemedText>
          {measurement.recommendations?.slice(0, 3).map((shoe: any, index: number) => (
            <ThemedText key={index} variant="bodySmall">
              • {shoe.brand} {shoe.model}
            </ThemedText>
          ))}
        </View>
      </ThemedView>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Clean Header */}
      <ThemedView style={styles.header}>
        <View style={styles.headerContent}>
          <IconSymbol
            size={32}
            name="clock.fill"
            color={colors.primary}
          />
          <ThemedText variant="h2">History</ThemedText>
        </View>
      </ThemedView>

      {loading || loadingFavorites ? (
        <LoadingScreen text={loading ? "Loading your measurements..." : "Loading favorites..."} />
      ) : error ? (
        <ThemedView style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={48} color={colors.error} />
          <ThemedText variant="h3" style={[styles.errorTitle, { color: colors.error }]}>
            Unable to Load Data
          </ThemedText>
          <ThemedText variant="body" style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </ThemedText>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              setError(null);
              loadMeasurements();
            }}
          >
            <ThemedText variant="button" style={{ color: colors.background }}>
              Try Again
            </ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[
            styles.scrollContent,
            { paddingBottom: Math.max(bottom + 10, 30) }
          ]}
          automaticallyAdjustContentInsets={false}
          contentInsetAdjustmentBehavior="never"
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={colors.primary}
            />
          }
        >
          {/* Filter Controls */}
          <ThemedView style={[styles.filtersSection, { borderColor: colors.border }]}>
            <View style={styles.filtersSectionHeader}>
              <ThemedText variant="h4" style={styles.filtersSectionTitle}>
                Filter Results
              </ThemedText>
              <TouchableOpacity
                style={[styles.favoritesToggle, {
                  backgroundColor: showFavoritesOnly ? colors.primary : colors.backgroundSecondary
                }]}
                onPress={() => setShowFavoritesOnly(!showFavoritesOnly)}
                activeOpacity={0.7}
              >
                <IconSymbol
                  size={16}
                  name={showFavoritesOnly ? "heart.fill" : "heart"}
                  color={showFavoritesOnly ? colors.background : colors.iconSecondary}
                />
                <ThemedText
                  variant="caption"
                  color={showFavoritesOnly ? 'inverse' : 'secondary'}
                  style={styles.favoritesToggleText}
                >
                  {showFavoritesOnly ? `Favorites (${favorites.size})` : 'Show All'}
                </ThemedText>
              </TouchableOpacity>
            </View>

            {/* Dropdowns Row */}
            <View style={styles.filtersGrid}>
              <View style={styles.filterColumn}>
                <Dropdown
                  label="Category"
                  options={categoryOptions}
                  selectedValue={filterCategory}
                  onSelect={setFilterCategory}
                  placeholder="All Categories"
                />
              </View>

              <View style={styles.filterColumn}>
                <Dropdown
                  label="Brand"
                  options={brandOptions}
                  selectedValue={filterBrand}
                  onSelect={setFilterBrand}
                  placeholder="All Brands"
                />
              </View>
            </View>

            {/* Action Buttons Row */}
            <View style={styles.actionButtonsRow}>
              {/* Clear Filters Button */}
              <TouchableOpacity
                style={[styles.clearFiltersButton, {
                  backgroundColor: colors.backgroundSecondary,
                  borderColor: colors.border
                }]}
                onPress={() => {
                  setFilterBrand('all');
                  setFilterCategory('all');
                  setShowFavoritesOnly(false);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                activeOpacity={0.7}
              >
                <IconSymbol
                  size={16}
                  name="xmark.circle"
                  color={colors.iconSecondary}
                />
                <ThemedText variant="caption" color="secondary">
                  Clear Filters
                </ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>

          {/* Content */}
          {(() => {
            const filteredMeasurements = getFilteredMeasurements();
            return filteredMeasurements.length > 0 ? (
              <View style={styles.content}>
                {filteredMeasurements.map(renderMeasurementCard)}
              </View>
            ) : measurements.length > 0 ? (
              <ThemedView style={styles.emptyState}>
                <IconSymbol
                  size={64}
                  name="line.3.horizontal.decrease.circle"
                  color={colors.iconSecondary}
                />
                <ThemedText variant="h3" color="secondary" style={styles.emptyTitle}>
                  No matching measurements
                </ThemedText>
                <ThemedText variant="body" color="tertiary" style={styles.emptyDescription}>
                  Try adjusting your filters to see more results
                </ThemedText>
              </ThemedView>
            ) : (
              <ThemedView style={styles.emptyState}>
                <IconSymbol
                  size={64}
                  name="clock"
                  color={colors.iconSecondary}
                />
                <ThemedText variant="h3" color="secondary" style={styles.emptyTitle}>
                  No measurements yet
                </ThemedText>
                <ThemedText variant="body" color="tertiary" style={styles.emptyDescription}>
                  Start by taking your first foot measurement on the Home tab
                </ThemedText>
              </ThemedView>
            );
          })()}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  filtersSection: {
    margin: 20,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  filtersSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  filtersSectionTitle: {
    flex: 1,
  },
  favoritesToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  favoritesToggleText: {
    fontSize: 12,
  },
  filtersGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  filterColumn: {
    flex: 1,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4,
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    gap: 6,
    minHeight: 48,
    minWidth: 140,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    padding: 20,
    gap: 16,
  },
  cardContainer: {
    borderRadius: 12,
  },
  card: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    gap: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardHeaderLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardHeaderInfo: {
    flex: 1,
  },
  tapHint: {
    marginRight: 8,
    opacity: 0.6,
  },
  cardActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  measurements: {
    flexDirection: 'row',
    gap: 24,
  },
  measurementItem: {
    gap: 4,
  },
  recommendations: {
    gap: 4,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    gap: 16,
  },
  emptyTitle: {
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    maxWidth: 280,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    gap: 16,
  },
  errorTitle: {
    textAlign: 'center',
  },
  errorMessage: {
    textAlign: 'center',
    maxWidth: 280,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
});
