/**
 * Environment variables validation and configuration
 * Consolidated environment management for FootFit
 *
 * Combines loading, validation, and configuration functionality
 */

import { log } from './logger';

// Environment variables for academic demonstration
// Only essential configurations for FootFit academic project
const ENV_CONFIG = {
  EXPO_PUBLIC_SUPABASE_URL: 'https://ajyfydsivhmpqjknqwzv.supabase.co',
  EXPO_PUBLIC_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqeWZ5ZHNpdmhtcHFqa25xd3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMTgxODUsImV4cCI6MjA2Njc5NDE4NX0.wMJmybTd8w2XNAS6khCE7UWR32fHLU3__vpGtKAJHm0',
  // AI API removed - using real TensorFlow.js CNN (client-side)
  // Sentry removed - not needed for academic demonstrations
};

/**
 * Load environment variables with fallback support
 */
function loadEnvironmentVariables() {
  // Ensure environment variables are available
  Object.entries(ENV_CONFIG).forEach(([key, value]) => {
    if (!process.env[key]) {
      // @ts-ignore - Setting process.env dynamically
      process.env[key] = value;
    }
  });
}

/**
 * Get environment variable with fallback
 */
function getEnvVar(key: keyof typeof ENV_CONFIG): string {
  return process.env[key] || ENV_CONFIG[key];
}

// Load environment variables immediately
loadEnvironmentVariables();

interface EnvironmentConfig {
  // Supabase
  supabaseUrl: string;
  supabaseAnonKey: string;

  // App Configuration
  isDevelopment: boolean;
  isProduction: boolean;
  appVersion: string;
}

class EnvironmentValidator {
  private static instance: EnvironmentValidator;
  private config: EnvironmentConfig | null = null;

  private constructor() {}

  static getInstance(): EnvironmentValidator {
    if (!EnvironmentValidator.instance) {
      EnvironmentValidator.instance = new EnvironmentValidator();
    }
    return EnvironmentValidator.instance;
  }

  validateAndGetConfig(): EnvironmentConfig {
    if (this.config) {
      return this.config;
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // Ensure environment variables are loaded
    loadEnvironmentVariables();

    // Get environment variables using the consolidated loader
    const supabaseUrl = getEnvVar('EXPO_PUBLIC_SUPABASE_URL');
    const supabaseAnonKey = getEnvVar('EXPO_PUBLIC_SUPABASE_ANON_KEY');

    // Log environment variable source for debugging
    log.info('Environment variables loaded', 'ENV', {
      source: 'envLoader',
      supabaseConfigured: !!(supabaseUrl && supabaseAnonKey),
    });

    if (!supabaseUrl) {
      errors.push('EXPO_PUBLIC_SUPABASE_URL is required');
    } else if (!this.isValidUrl(supabaseUrl)) {
      errors.push('EXPO_PUBLIC_SUPABASE_URL must be a valid URL');
    }

    if (!supabaseAnonKey) {
      errors.push('EXPO_PUBLIC_SUPABASE_ANON_KEY is required');
    } else if (supabaseAnonKey.length < 50) {
      warnings.push('EXPO_PUBLIC_SUPABASE_ANON_KEY seems too short');
    }

    // Note: AI service is now fully offline - no external API required

    // Environment detection
    const isDevelopment = __DEV__ || process.env.NODE_ENV === 'development';
    const isProduction = process.env.NODE_ENV === 'production';

    // App version
    const appVersion = process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0';

    // Log validation results
    if (errors.length > 0) {
      log.error('Environment validation failed', 'ENV', { errors });
      throw new Error(`Environment validation failed: ${errors.join(', ')}`);
    }

    if (warnings.length > 0) {
      log.warn('Environment validation warnings', 'ENV', { warnings });
    }

    this.config = {
      supabaseUrl: supabaseUrl!,
      supabaseAnonKey: supabaseAnonKey!,
      isDevelopment,
      isProduction,
      appVersion,
    };

    log.info('Environment validation successful', 'ENV', {
      isDevelopment,
      isProduction,
      appVersion,
      aiMode: 'TensorFlow.js CNN (client-side)',
      supabaseConfigured: true,
    });

    return this.config;
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Convenience getters
  get supabaseUrl(): string {
    return this.validateAndGetConfig().supabaseUrl;
  }

  get supabaseAnonKey(): string {
    return this.validateAndGetConfig().supabaseAnonKey;
  }

  get isDevelopment(): boolean {
    return this.validateAndGetConfig().isDevelopment;
  }

  get isProduction(): boolean {
    return this.validateAndGetConfig().isProduction;
  }

  get appVersion(): string {
    return this.validateAndGetConfig().appVersion;
  }
}

// Export singleton instance
export const env = EnvironmentValidator.getInstance();

// Validate environment on module load
try {
  env.validateAndGetConfig();
} catch (error) {
  // Use basic console.error here since logger might not be available during module initialization
  console.error('❌ Environment validation failed:', error);
  // In development, we can continue with warnings
  // In production, this should crash the app
  if (process.env.NODE_ENV === 'production') {
    throw error;
  }
}
