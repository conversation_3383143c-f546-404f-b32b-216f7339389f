/**
 * Model Training Pipeline for FootFit CNN
 * Trains a real CNN model using actual foot image datasets
 * Replaces placeholder weights with genuine trained model
 */

const tf = require('@tensorflow/tfjs-node');
const fs = require('fs');
const path = require('path');

// Training configuration
const CONFIG = {
  datasetPath: path.join(__dirname, '..', 'datasets'),
  modelOutputPath: path.join(__dirname, '..', 'assets', 'models'),
  batchSize: 32,
  epochs: 50,
  learningRate: 0.001,
  validationSplit: 0.2,
  imageSize: [224, 224],
  channels: 3,
  outputSize: 4, // [foot_length, foot_width, confidence, quality]
  
  // Data augmentation settings
  augmentation: {
    rotation: 0.1,
    brightness: 0.2,
    contrast: 0.2,
    flip: true
  },

  // Early stopping
  patience: 10,
  minDelta: 0.001
};

class FootMeasurementTrainer {
  constructor() {
    this.model = null;
    this.trainDataset = null;
    this.validationDataset = null;
    this.trainingHistory = [];
  }

  /**
   * Main training pipeline
   */
  async train() {
    console.log('🚀 Starting FootFit CNN Training Pipeline...');
    console.log(`Dataset: ${CONFIG.datasetPath}`);
    console.log(`Output: ${CONFIG.modelOutputPath}`);

    try {
      // Step 1: Load and prepare datasets
      await this.loadDatasets();

      // Step 2: Create CNN model architecture
      this.createModel();

      // Step 3: Compile model
      this.compileModel();

      // Step 4: Train model
      await this.trainModel();

      // Step 5: Evaluate model
      await this.evaluateModel();

      // Step 6: Save trained model
      await this.saveModel();

      console.log('✅ Training completed successfully!');
      this.printTrainingResults();

    } catch (error) {
      console.error('❌ Training failed:', error.message);
      throw error;
    }
  }

  /**
   * Load and prepare training datasets
   */
  async loadDatasets() {
    console.log('📊 Loading datasets...');

    // Load training data
    const trainImages = await this.loadImages('train');
    const trainLabels = await this.loadAnnotations('train');

    // Load validation data
    const valImages = await this.loadImages('validation');
    const valLabels = await this.loadAnnotations('validation');

    console.log(`Training samples: ${trainImages.length}`);
    console.log(`Validation samples: ${valImages.length}`);

    // Create TensorFlow datasets
    this.trainDataset = this.createTensorDataset(trainImages, trainLabels, true);
    this.validationDataset = this.createTensorDataset(valImages, valLabels, false);

    console.log('✅ Datasets loaded successfully');
  }

  /**
   * Load images from dataset directory
   */
  async loadImages(split) {
    const imagesPath = path.join(CONFIG.datasetPath, split, 'images');
    const imageFiles = fs.readdirSync(imagesPath)
      .filter(file => ['.jpg', '.jpeg', '.png'].includes(path.extname(file).toLowerCase()));

    const images = [];
    
    for (const file of imageFiles) {
      const imagePath = path.join(imagesPath, file);
      try {
        // Load and preprocess image
        const imageBuffer = fs.readFileSync(imagePath);
        const imageTensor = tf.node.decodeImage(imageBuffer, CONFIG.channels);
        
        // Resize to target size
        const resized = tf.image.resizeBilinear(imageTensor, CONFIG.imageSize);
        
        // Normalize to [0, 1]
        const normalized = resized.div(255.0);
        
        images.push(normalized);
        
        // Clean up intermediate tensors
        imageTensor.dispose();
        resized.dispose();
        
      } catch (error) {
        console.warn(`Failed to load image ${file}:`, error.message);
      }
    }

    return images;
  }

  /**
   * Load annotations from dataset directory
   */
  async loadAnnotations(split) {
    const annotationsPath = path.join(CONFIG.datasetPath, split, 'annotations');
    const annotationFiles = fs.readdirSync(annotationsPath)
      .filter(file => file.endsWith('.json'));

    const labels = [];

    for (const file of annotationFiles) {
      try {
        const annotationPath = path.join(annotationsPath, file);
        const annotation = JSON.parse(fs.readFileSync(annotationPath, 'utf8'));
        
        // Extract measurements and create label vector
        const measurements = annotation.measurements;
        const label = [
          measurements.foot_length_cm || 25.0,  // Default if missing
          measurements.foot_width_cm || 9.0,    // Default if missing
          0.9,  // Confidence (placeholder)
          0.8   // Quality (placeholder)
        ];

        labels.push(label);

      } catch (error) {
        console.warn(`Failed to load annotation ${file}:`, error.message);
        // Use default values for failed annotations
        labels.push([25.0, 9.0, 0.5, 0.5]);
      }
    }

    return labels;
  }

  /**
   * Create TensorFlow dataset from images and labels
   */
  createTensorDataset(images, labels, isTraining) {
    // Stack images and labels into tensors
    const imagesTensor = tf.stack(images);
    const labelsTensor = tf.tensor2d(labels);

    // Clean up individual image tensors
    images.forEach(img => img.dispose());

    // Create dataset
    let dataset = tf.data.zip({
      xs: tf.data.array(tf.unstack(imagesTensor)),
      ys: tf.data.array(tf.unstack(labelsTensor))
    });

    if (isTraining) {
      // Apply data augmentation for training
      dataset = dataset.map(({xs, ys}) => ({
        xs: this.augmentImage(xs),
        ys: ys
      }));
      
      // Shuffle training data
      dataset = dataset.shuffle(1000);
    }

    // Batch the dataset
    dataset = dataset.batch(CONFIG.batchSize);

    // Clean up tensors
    imagesTensor.dispose();
    labelsTensor.dispose();

    return dataset;
  }

  /**
   * Apply data augmentation to images
   */
  augmentImage(image) {
    let augmented = image;

    // Random rotation
    if (Math.random() < 0.5) {
      const angle = (Math.random() - 0.5) * CONFIG.augmentation.rotation;
      augmented = tf.image.rotateWithOffset(augmented, angle);
    }

    // Random brightness adjustment
    if (Math.random() < 0.5) {
      const delta = (Math.random() - 0.5) * CONFIG.augmentation.brightness;
      augmented = tf.image.adjustBrightness(augmented, delta);
    }

    // Random horizontal flip
    if (Math.random() < 0.5 && CONFIG.augmentation.flip) {
      augmented = tf.image.flipLeftRight(augmented);
    }

    return augmented;
  }

  /**
   * Create CNN model architecture
   */
  createModel() {
    console.log('🏗️  Creating CNN model architecture...');

    this.model = tf.sequential({
      layers: [
        // Input layer
        tf.layers.inputLayer({
          inputShape: [...CONFIG.imageSize, CONFIG.channels]
        }),

        // First convolutional block
        tf.layers.conv2d({
          filters: 32,
          kernelSize: 3,
          strides: 2,
          padding: 'same',
          activation: 'relu',
          name: 'conv2d_1'
        }),
        tf.layers.batchNormalization(),

        // Second convolutional block
        tf.layers.conv2d({
          filters: 64,
          kernelSize: 3,
          strides: 2,
          padding: 'same',
          activation: 'relu',
          name: 'conv2d_2'
        }),
        tf.layers.batchNormalization(),

        // Third convolutional block
        tf.layers.conv2d({
          filters: 128,
          kernelSize: 3,
          strides: 2,
          padding: 'same',
          activation: 'relu',
          name: 'conv2d_3'
        }),
        tf.layers.batchNormalization(),

        // Global average pooling
        tf.layers.globalAveragePooling2d({
          name: 'global_average_pooling2d_1'
        }),

        // Dense layers
        tf.layers.dense({
          units: 128,
          activation: 'relu',
          name: 'dense_1'
        }),
        tf.layers.dropout({
          rate: 0.3,
          name: 'dropout_1'
        }),

        // Output layer
        tf.layers.dense({
          units: CONFIG.outputSize,
          activation: 'linear',
          name: 'output'
        })
      ]
    });

    console.log('✅ Model architecture created');
    this.model.summary();
  }

  /**
   * Compile model with optimizer and loss function
   */
  compileModel() {
    console.log('⚙️  Compiling model...');

    this.model.compile({
      optimizer: tf.train.adam(CONFIG.learningRate),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    console.log('✅ Model compiled');
  }

  /**
   * Train the model
   */
  async trainModel() {
    console.log('🎯 Starting model training...');

    const callbacks = [
      // Early stopping
      tf.callbacks.earlyStopping({
        monitor: 'val_loss',
        patience: CONFIG.patience,
        minDelta: CONFIG.minDelta,
        restoreBestWeights: true
      }),

      // Learning rate reduction
      tf.callbacks.reduceLROnPlateau({
        monitor: 'val_loss',
        factor: 0.5,
        patience: 5,
        minLr: 0.00001
      })
    ];

    const history = await this.model.fitDataset(this.trainDataset, {
      epochs: CONFIG.epochs,
      validationData: this.validationDataset,
      callbacks: callbacks,
      verbose: 1
    });

    this.trainingHistory = history.history;
    console.log('✅ Training completed');
  }

  /**
   * Evaluate model performance
   */
  async evaluateModel() {
    console.log('📈 Evaluating model...');

    // Load test data
    const testImages = await this.loadImages('test');
    const testLabels = await this.loadAnnotations('test');
    const testDataset = this.createTensorDataset(testImages, testLabels, false);

    const evaluation = await this.model.evaluateDataset(testDataset);
    const [loss, mae] = await Promise.all(evaluation.map(tensor => tensor.data()));

    console.log(`Test Loss: ${loss[0].toFixed(4)}`);
    console.log(`Test MAE: ${mae[0].toFixed(4)}`);

    // Clean up
    evaluation.forEach(tensor => tensor.dispose());
  }

  /**
   * Save trained model
   */
  async saveModel() {
    console.log('💾 Saving trained model...');

    // Ensure output directory exists
    if (!fs.existsSync(CONFIG.modelOutputPath)) {
      fs.mkdirSync(CONFIG.modelOutputPath, { recursive: true });
    }

    // Save model in TensorFlow.js format
    const modelUrl = `file://${CONFIG.modelOutputPath}`;
    await this.model.save(modelUrl);

    console.log(`✅ Model saved to: ${CONFIG.modelOutputPath}`);
  }

  /**
   * Print training results summary
   */
  printTrainingResults() {
    console.log('\n📊 Training Results Summary:');
    console.log('='.repeat(50));
    
    const finalEpoch = this.trainingHistory.loss.length - 1;
    console.log(`Final Training Loss: ${this.trainingHistory.loss[finalEpoch].toFixed(4)}`);
    console.log(`Final Validation Loss: ${this.trainingHistory.val_loss[finalEpoch].toFixed(4)}`);
    console.log(`Final Training MAE: ${this.trainingHistory.mae[finalEpoch].toFixed(4)}`);
    console.log(`Final Validation MAE: ${this.trainingHistory.val_mae[finalEpoch].toFixed(4)}`);
    console.log(`Total Epochs: ${finalEpoch + 1}`);
    
    console.log('\n🎯 Model is ready for deployment!');
    console.log('The trained weights have replaced the placeholder model.');
  }
}

// Run training if called directly
if (require.main === module) {
  const trainer = new FootMeasurementTrainer();
  trainer.train().catch(console.error);
}

module.exports = FootMeasurementTrainer;
