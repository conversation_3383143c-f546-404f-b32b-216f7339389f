/**
 * FootFit Shoe Data Service
 * Handles all shoe-related database operations with Supabase
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../lib/supabase';

// =====================================================
// TYPE DEFINITIONS
// =====================================================

export interface ShoeCategory {
  id: string;
  name: string;
  description?: string;
  display_order: number;
  icon_name?: string;
  color_theme?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ShoeBrand {
  id: string;
  name: string;
  logo_url?: string;
  website_url?: string;
  brand_description?: string;
  country_origin?: string;
  founded_year?: number;
  is_premium: boolean;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export interface ShoeModel {
  id: string;
  brand_id: string;
  category_id: string;
  model_name: string;
  model_code?: string;
  description?: string;
  image_url?: string;
  additional_images?: string[];
  price_range_min?: number;
  price_range_max?: number;
  currency: string;
  availability_status: 'available' | 'limited' | 'discontinued';
  fit_type: 'narrow' | 'regular' | 'wide';
  target_gender: 'men' | 'women' | 'unisex' | 'kids';
  release_date?: string;
  popularity_score: number;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Joined data
  brand?: ShoeBrand;
  category?: ShoeCategory;
}

export interface ShoeSize {
  id: string;
  model_id: string;
  size_us?: string;
  size_uk?: string;
  size_eu?: string;
  size_cm?: number;
  is_available: boolean;
  stock_level: number;
}

export interface UserShoePreferences {
  id: string;
  user_id: string;
  preferred_brands: string[];
  preferred_categories: string[];
  price_range_min?: number;
  price_range_max?: number;
  preferred_fit_type: 'narrow' | 'regular' | 'wide';
  preferred_colors: string[];
  avoid_brands: string[];
}

export interface RecommendationRequest {
  foot_length: number;
  foot_width: number;
  user_id?: string;
  preferred_categories?: string[];
  preferred_brands?: string[];
  price_range_max?: number;
  fit_preference?: 'narrow' | 'regular' | 'wide';
}

export interface ShoeRecommendation {
  model: ShoeModel;
  confidence: number;
  recommended_size_us: string;
  recommended_size_uk: string;
  recommended_size_eu: string;
  fit_score: number;
  price_score: number;
  brand_score: number;
}

// =====================================================
// CACHING CONFIGURATION
// =====================================================

const CACHE_KEYS = {
  CATEGORIES: 'shoe_categories',
  BRANDS: 'shoe_brands',
  FEATURED_MODELS: 'featured_models',
  USER_PREFERENCES: 'user_preferences',
} as const;

const CACHE_DURATION = {
  CATEGORIES: 24 * 60 * 60 * 1000, // 24 hours
  BRANDS: 24 * 60 * 60 * 1000, // 24 hours
  MODELS: 6 * 60 * 60 * 1000, // 6 hours
  USER_DATA: 30 * 60 * 1000, // 30 minutes
} as const;

// =====================================================
// CACHE UTILITIES
// =====================================================

async function getCachedData<T>(key: string, maxAge: number): Promise<T | null> {
  try {
    const cached = await AsyncStorage.getItem(key);
    if (!cached) return null;

    const { data, timestamp } = JSON.parse(cached);
    if (Date.now() - timestamp > maxAge) {
      await AsyncStorage.removeItem(key);
      return null;
    }

    return data;
  } catch (error) {
    const { log } = await import('@/utils/logger');
    log.warn('Cache read error', 'ShoeDataService', error);
    return null;
  }
}

async function setCachedData<T>(key: string, data: T): Promise<void> {
  try {
    const cacheEntry = {
      data,
      timestamp: Date.now(),
    };
    await AsyncStorage.setItem(key, JSON.stringify(cacheEntry));
  } catch (error) {
    const { log } = await import('@/utils/logger');
    log.warn('Cache write error', 'ShoeDataService', error);
  }
}

// =====================================================
// CATEGORY OPERATIONS
// =====================================================

export class ShoeCategoryService {
  static async getCategories(useCache = true): Promise<ShoeCategory[]> {
    if (useCache) {
      const cached = await getCachedData<ShoeCategory[]>(
        CACHE_KEYS.CATEGORIES,
        CACHE_DURATION.CATEGORIES
      );
      if (cached) return cached;
    }

    const { data, error } = await supabase
      .from('shoe_categories')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (error) {
      console.error('Error fetching categories:', error);
      throw new Error('Failed to fetch shoe categories');
    }

    const categories = data || [];
    if (useCache) {
      await setCachedData(CACHE_KEYS.CATEGORIES, categories);
    }

    return categories;
  }

  static async getCategoryById(id: string): Promise<ShoeCategory | null> {
    const { data, error } = await supabase
      .from('shoe_categories')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return null;
    }

    return data;
  }

  static async createCategory(category: Omit<ShoeCategory, 'id' | 'created_at' | 'updated_at'>): Promise<ShoeCategory | null> {
    const { data, error } = await supabase
      .from('shoe_categories')
      .insert([category])
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      throw new Error('Failed to create category');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.CATEGORIES);
    return data;
  }

  static async updateCategory(id: string, updates: Partial<ShoeCategory>): Promise<ShoeCategory | null> {
    const { data, error } = await supabase
      .from('shoe_categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      throw new Error('Failed to update category');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.CATEGORIES);
    return data;
  }

  static async deleteCategory(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('shoe_categories')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting category:', error);
      return false;
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.CATEGORIES);
    return true;
  }
}

// =====================================================
// BRAND OPERATIONS
// =====================================================

export class ShoeBrandService {
  static async getBrands(useCache = true): Promise<ShoeBrand[]> {
    if (useCache) {
      const cached = await getCachedData<ShoeBrand[]>(
        CACHE_KEYS.BRANDS,
        CACHE_DURATION.BRANDS
      );
      if (cached) return cached;
    }

    const { data, error } = await supabase
      .from('shoe_brands')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (error) {
      console.error('Error fetching brands:', error);
      throw new Error('Failed to fetch shoe brands');
    }

    const brands = data || [];
    if (useCache) {
      await setCachedData(CACHE_KEYS.BRANDS, brands);
    }

    return brands;
  }

  static async getBrandsByCategory(categoryId: string): Promise<ShoeBrand[]> {
    const { data, error } = await supabase
      .from('brand_category_mapping')
      .select(`
        shoe_brands (*)
      `)
      .eq('category_id', categoryId)
      .eq('shoe_brands.is_active', true)
      .order('specialization_level', { ascending: false });

    if (error) {
      console.error('Error fetching brands by category:', error);
      throw new Error('Failed to fetch brands for category');
    }

    return data?.map(item => item.shoe_brands).flat().filter(Boolean) || [];
  }

  static async getBrandById(id: string): Promise<ShoeBrand | null> {
    const { data, error } = await supabase
      .from('shoe_brands')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching brand:', error);
      return null;
    }

    return data;
  }

  static async createBrand(brand: Omit<ShoeBrand, 'id' | 'created_at' | 'updated_at'>): Promise<ShoeBrand | null> {
    const { data, error } = await supabase
      .from('shoe_brands')
      .insert([brand])
      .select()
      .single();

    if (error) {
      console.error('Error creating brand:', error);
      throw new Error('Failed to create brand');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.BRANDS);
    return data;
  }

  static async updateBrand(id: string, updates: Partial<ShoeBrand>): Promise<ShoeBrand | null> {
    const { data, error } = await supabase
      .from('shoe_brands')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating brand:', error);
      throw new Error('Failed to update brand');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.BRANDS);
    return data;
  }

  static async deleteBrand(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('shoe_brands')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting brand:', error);
      return false;
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.BRANDS);
    return true;
  }

  /**
   * Convert brand names to brand IDs for database operations
   */
  static async getBrandIdsByNames(brandNames: string[]): Promise<string[]> {
    if (!brandNames || brandNames.length === 0) return [];

    const { data, error } = await supabase
      .from('shoe_brands')
      .select('id, name')
      .in('name', brandNames)
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching brand IDs:', error);
      return [];
    }

    return data?.map(brand => brand.id) || [];
  }

  /**
   * Convert brand IDs to brand names for display
   */
  static async getBrandNamesByIds(brandIds: string[]): Promise<string[]> {
    if (!brandIds || brandIds.length === 0) return [];

    const { data, error } = await supabase
      .from('shoe_brands')
      .select('id, name')
      .in('id', brandIds)
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching brand names:', error);
      return [];
    }

    return data?.map(brand => brand.name) || [];
  }

  /**
   * Get brand mapping for preference conversion
   */
  static async getBrandMapping(): Promise<Map<string, string>> {
    const brands = await this.getBrands();
    const mapping = new Map<string, string>();

    brands.forEach(brand => {
      mapping.set(brand.name, brand.id);
    });

    return mapping;
  }
}

// =====================================================
// SHOE MODEL OPERATIONS
// =====================================================

export class ShoeModelService {
  static async getModels(filters?: {
    categoryId?: string;
    brandId?: string;
    availability?: string;
    featured?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<ShoeModel[]> {
    let query = supabase
      .from('shoe_models')
      .select(`
        *,
        brand:shoe_brands(*),
        category:shoe_categories(*)
      `)
      .eq('is_active', true);

    if (filters?.categoryId) {
      query = query.eq('category_id', filters.categoryId);
    }

    if (filters?.brandId) {
      query = query.eq('brand_id', filters.brandId);
    }

    if (filters?.availability) {
      query = query.eq('availability_status', filters.availability);
    }

    if (filters?.featured) {
      query = query.eq('is_featured', true);
    }

    query = query.order('popularity_score', { ascending: false });

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    if (filters?.offset) {
      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching models:', error);
      throw new Error('Failed to fetch shoe models');
    }

    return data || [];
  }

  static async getFeaturedModels(useCache = true): Promise<ShoeModel[]> {
    if (useCache) {
      const cached = await getCachedData<ShoeModel[]>(
        CACHE_KEYS.FEATURED_MODELS,
        CACHE_DURATION.MODELS
      );
      if (cached) return cached;
    }

    const models = await this.getModels({ featured: true, limit: 20 });

    if (useCache) {
      await setCachedData(CACHE_KEYS.FEATURED_MODELS, models);
    }

    return models;
  }

  static async getModelById(id: string): Promise<ShoeModel | null> {
    const { data, error } = await supabase
      .from('shoe_models')
      .select(`
        *,
        brand:shoe_brands(*),
        category:shoe_categories(*)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching model:', error);
      return null;
    }

    return data;
  }

  static async searchModels(searchTerm: string, filters?: {
    categoryId?: string;
    brandId?: string;
    limit?: number;
  }): Promise<ShoeModel[]> {
    let query = supabase
      .from('shoe_models')
      .select(`
        *,
        brand:shoe_brands(*),
        category:shoe_categories(*)
      `)
      .eq('is_active', true)
      .or(`model_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);

    if (filters?.categoryId) {
      query = query.eq('category_id', filters.categoryId);
    }

    if (filters?.brandId) {
      query = query.eq('brand_id', filters.brandId);
    }

    query = query.order('popularity_score', { ascending: false });

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error searching models:', error);
      throw new Error('Failed to search shoe models');
    }

    return data || [];
  }

  static async createModel(model: Omit<ShoeModel, 'id' | 'created_at' | 'updated_at' | 'brand' | 'category'>): Promise<ShoeModel | null> {
    const { data, error } = await supabase
      .from('shoe_models')
      .insert([model])
      .select(`
        *,
        brand:shoe_brands(*),
        category:shoe_categories(*)
      `)
      .single();

    if (error) {
      console.error('Error creating model:', error);
      throw new Error('Failed to create shoe model');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.FEATURED_MODELS);
    return data;
  }

  static async updateModel(id: string, updates: Partial<ShoeModel>): Promise<ShoeModel | null> {
    const { data, error } = await supabase
      .from('shoe_models')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        brand:shoe_brands(*),
        category:shoe_categories(*)
      `)
      .single();

    if (error) {
      console.error('Error updating model:', error);
      throw new Error('Failed to update shoe model');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.FEATURED_MODELS);
    return data;
  }

  static async deleteModel(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('shoe_models')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting model:', error);
      return false;
    }

    // Invalidate cache
    await AsyncStorage.removeItem(CACHE_KEYS.FEATURED_MODELS);
    return true;
  }
}

// =====================================================
// USER PREFERENCES OPERATIONS
// =====================================================

export class UserPreferencesService {
  static async getUserPreferences(userId: string, useCache = true): Promise<UserShoePreferences | null> {
    if (useCache) {
      const cached = await getCachedData<UserShoePreferences>(
        `${CACHE_KEYS.USER_PREFERENCES}_${userId}`,
        CACHE_DURATION.USER_DATA
      );
      if (cached) return cached;
    }

    const { data, error } = await supabase
      .from('user_shoe_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching user preferences:', error);
      return null;
    }

    const preferences = data || null;
    if (useCache && preferences) {
      await setCachedData(`${CACHE_KEYS.USER_PREFERENCES}_${userId}`, preferences);
    }

    return preferences;
  }

  static async saveUserPreferences(preferences: Omit<UserShoePreferences, 'id' | 'created_at' | 'updated_at'>): Promise<UserShoePreferences | null> {
    const { data, error } = await supabase
      .from('user_shoe_preferences')
      .upsert([preferences])
      .select()
      .single();

    if (error) {
      console.error('Error saving user preferences:', error);
      throw new Error('Failed to save user preferences');
    }

    // Invalidate cache
    await AsyncStorage.removeItem(`${CACHE_KEYS.USER_PREFERENCES}_${preferences.user_id}`);
    return data;
  }

  /**
   * Convert profile brand preferences (names) to Supabase preferences (UUIDs)
   */
  static async convertProfileToSupabasePreferences(
    userId: string,
    profileBrands: string[],
    selectedCategories?: string[]
  ): Promise<UserShoePreferences | null> {
    try {
      // Convert brand names to IDs
      const brandIds = await ShoeBrandService.getBrandIdsByNames(profileBrands);

      // Convert category names to IDs if provided
      let categoryIds: string[] = [];
      if (selectedCategories && selectedCategories.length > 0) {
        const { data: categories } = await supabase
          .from('shoe_categories')
          .select('id, name')
          .in('name', selectedCategories)
          .eq('is_active', true);

        categoryIds = categories?.map(cat => cat.id) || [];
      }

      const preferences: Omit<UserShoePreferences, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        preferred_brands: brandIds,
        preferred_categories: categoryIds,
        preferred_fit_type: 'regular',
        preferred_colors: [],
        avoid_brands: []
      };

      return await this.saveUserPreferences(preferences);
    } catch (error) {
      console.error('Error converting profile preferences:', error);
      return null;
    }
  }

  /**
   * Get enhanced user preferences with brand and category names for display
   */
  static async getEnhancedUserPreferences(userId: string): Promise<{
    preferences: UserShoePreferences | null;
    brandNames: string[];
    categoryNames: string[];
  }> {
    const preferences = await this.getUserPreferences(userId);

    if (!preferences) {
      return {
        preferences: null,
        brandNames: [],
        categoryNames: []
      };
    }

    // Convert brand IDs to names
    const brandNames = await ShoeBrandService.getBrandNamesByIds(preferences.preferred_brands);

    // Convert category IDs to names
    let categoryNames: string[] = [];
    if (preferences.preferred_categories.length > 0) {
      const { data: categories } = await supabase
        .from('shoe_categories')
        .select('id, name')
        .in('id', preferences.preferred_categories)
        .eq('is_active', true);

      categoryNames = categories?.map(cat => cat.name) || [];
    }

    return {
      preferences,
      brandNames,
      categoryNames
    };
  }

  static async deleteUserPreferences(userId: string): Promise<boolean> {
    const { error } = await supabase
      .from('user_shoe_preferences')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Error deleting user preferences:', error);
      return false;
    }

    // Invalidate cache
    await AsyncStorage.removeItem(`${CACHE_KEYS.USER_PREFERENCES}_${userId}`);
    return true;
  }
}

// =====================================================
// INTELLIGENT RECOMMENDATION SERVICE
// =====================================================

export class ShoeRecommendationService {
  static async getRecommendations(request: RecommendationRequest): Promise<ShoeRecommendation[]> {
    try {
      // Get user preferences if user_id provided
      let userPreferences: UserShoePreferences | null = null;
      if (request.user_id) {
        userPreferences = await UserPreferencesService.getUserPreferences(request.user_id);
      }

      // Build filters based on request and preferences
      const filters: any = {
        availability: 'available',
        limit: 50, // Get more models for better recommendation quality
      };

      // Apply category filters
      const preferredCategories = request.preferred_categories || userPreferences?.preferred_categories;
      if (preferredCategories && preferredCategories.length > 0) {
        // We'll filter after fetching since Supabase doesn't support IN with arrays easily
      }

      // Apply brand filters
      const preferredBrands = request.preferred_brands || userPreferences?.preferred_brands;
      if (preferredBrands && preferredBrands.length > 0) {
        // We'll filter after fetching
      }

      // Fetch candidate models
      let candidateModels = await ShoeModelService.getModels(filters);

      // Apply client-side filtering
      if (preferredCategories && preferredCategories.length > 0) {
        candidateModels = candidateModels.filter(model =>
          preferredCategories.includes(model.category_id)
        );
      }

      if (preferredBrands && preferredBrands.length > 0) {
        candidateModels = candidateModels.filter(model =>
          preferredBrands.includes(model.brand_id)
        );
      }

      // Apply price filtering
      const maxPrice = request.price_range_max || userPreferences?.price_range_max;
      if (maxPrice) {
        candidateModels = candidateModels.filter(model =>
          !model.price_range_min || model.price_range_min <= maxPrice
        );
      }

      // Calculate recommendations with scoring
      const recommendations: ShoeRecommendation[] = [];

      for (const model of candidateModels) {
        const recommendation = await this.calculateRecommendationScore(
          model,
          request,
          userPreferences
        );

        if (recommendation.confidence > 0.3) { // Minimum confidence threshold
          recommendations.push(recommendation);
        }
      }

      // Sort by confidence and return top recommendations
      recommendations.sort((a, b) => b.confidence - a.confidence);
      return recommendations.slice(0, 10); // Return top 10 recommendations

    } catch (error) {
      console.error('Error generating recommendations:', error);
      throw new Error('Failed to generate shoe recommendations');
    }
  }

  private static async calculateRecommendationScore(
    model: ShoeModel,
    request: RecommendationRequest,
    userPreferences: UserShoePreferences | null
  ): Promise<ShoeRecommendation> {
    // Calculate size recommendation based on foot measurements
    const sizeRecommendation = this.calculateSizeRecommendation(
      request.foot_length,
      request.foot_width,
      model.fit_type
    );

    // Calculate various scoring factors
    const fitScore = this.calculateFitScore(request, model, userPreferences);
    const brandScore = this.calculateBrandScore(model, userPreferences);
    const priceScore = this.calculatePriceScore(model, userPreferences);
    const popularityScore = model.popularity_score / 100; // Normalize to 0-1

    // Calculate overall confidence
    const confidence = (
      fitScore * 0.4 +           // 40% weight on fit
      brandScore * 0.25 +        // 25% weight on brand preference
      priceScore * 0.2 +         // 20% weight on price
      popularityScore * 0.15     // 15% weight on popularity
    );

    return {
      model,
      confidence: Math.round(confidence * 100) / 100,
      recommended_size_us: sizeRecommendation.us,
      recommended_size_uk: sizeRecommendation.uk,
      recommended_size_eu: sizeRecommendation.eu,
      fit_score: Math.round(fitScore * 100) / 100,
      price_score: Math.round(priceScore * 100) / 100,
      brand_score: Math.round(brandScore * 100) / 100,
    };
  }

  private static calculateSizeRecommendation(
    footLength: number,
    footWidth: number,
    fitType: string
  ): { us: string; uk: string; eu: string } {
    // Basic size calculation algorithm
    // This is a simplified version - in production, you'd want more sophisticated sizing

    let baseSize = footLength - 1.5; // Basic conversion from cm to US size

    // Adjust for fit type
    switch (fitType) {
      case 'narrow':
        baseSize += 0.5;
        break;
      case 'wide':
        baseSize -= 0.5;
        break;
      default: // regular
        break;
    }

    // Adjust for foot width
    if (footWidth > 10.5) {
      baseSize += 0.5;
    } else if (footWidth < 8.5) {
      baseSize -= 0.5;
    }

    // Convert to different sizing systems
    const usSize = Math.round(baseSize * 2) / 2; // Round to nearest 0.5
    const ukSize = usSize - 1;
    const euSize = usSize + 32;

    return {
      us: usSize.toString(),
      uk: ukSize.toString(),
      eu: Math.round(euSize).toString(),
    };
  }

  private static calculateFitScore(
    request: RecommendationRequest,
    model: ShoeModel,
    userPreferences: UserShoePreferences | null
  ): number {
    let score = 0.7; // Base score

    // Fit type preference
    const preferredFit = request.fit_preference || userPreferences?.preferred_fit_type || 'regular';
    if (model.fit_type === preferredFit) {
      score += 0.3;
    } else if (
      (preferredFit === 'regular' && model.fit_type !== 'narrow') ||
      (preferredFit === 'wide' && model.fit_type === 'regular')
    ) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  private static calculateBrandScore(
    model: ShoeModel,
    userPreferences: UserShoePreferences | null
  ): number {
    if (!userPreferences) return 0.5; // Neutral score

    // Check if brand is preferred
    if (userPreferences.preferred_brands?.includes(model.brand_id)) {
      return 1.0;
    }

    // Check if brand should be avoided
    if (userPreferences.avoid_brands?.includes(model.brand_id)) {
      return 0.1;
    }

    // Premium brand bonus
    if (model.brand?.is_premium) {
      return 0.7;
    }

    return 0.5; // Neutral score
  }

  private static calculatePriceScore(
    model: ShoeModel,
    userPreferences: UserShoePreferences | null
  ): number {
    if (!userPreferences?.price_range_max || !model.price_range_min) {
      return 0.5; // Neutral score if no price info
    }

    const userMaxPrice = userPreferences.price_range_max;
    const modelMinPrice = model.price_range_min;

    if (modelMinPrice <= userMaxPrice * 0.7) {
      return 1.0; // Great value
    } else if (modelMinPrice <= userMaxPrice) {
      return 0.7; // Within budget
    } else {
      return 0.2; // Over budget
    }
  }
}

// =====================================================
// CONVENIENCE FUNCTIONS FOR BACKWARD COMPATIBILITY
// =====================================================

/**
 * Get shoe brands for dropdown/filter options
 * @deprecated Use ShoeBrandService.getBrands() instead
 */
export async function getShoeBrands(): Promise<Array<{ name: string; id: string }>> {
  const brands = await ShoeBrandService.getBrands();
  return brands.map(brand => ({ name: brand.name, id: brand.id }));
}

/**
 * Get shoe categories for dropdown/filter options
 * @deprecated Use ShoeCategoryService.getCategories() instead
 */
export async function getShoeCategories(): Promise<Array<{ name: string; id: string }>> {
  const categories = await ShoeCategoryService.getCategories();
  return categories.map(category => ({ name: category.name, id: category.id }));
}
