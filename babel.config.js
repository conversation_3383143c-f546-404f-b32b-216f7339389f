module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      // Add support for dynamic imports
      '@babel/plugin-syntax-dynamic-import',
      // Handle TensorFlow.js modules
      [
        'babel-plugin-module-resolver',
        {
          alias: {
            // Resolve TensorFlow.js platform issues
            '@tensorflow/tfjs-platform-react-native': '@tensorflow/tfjs-react-native/dist/platform_react_native',
          },
        },
      ],
      // React Native Reanimated plugin (should be last)
      'react-native-reanimated/plugin',
    ],
  };
};
