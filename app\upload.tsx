import { useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { useState } from 'react';
import { Alert, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useTheme } from '@/contexts/ThemeContext';


export default function UploadScreen() {
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(false);

  // Use unified camera permission system
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();



  const requestPermissions = async () => {
    try {
      // Use unified camera permission system
      if (!cameraPermission?.granted) {
        console.log('Requesting camera permission...');
        const result = await requestCameraPermission();
        if (!result.granted) {
          Alert.alert(
            'Camera Permission Required',
            'Please allow camera access to take photos of your foot.',
            [{ text: 'OK' }]
          );
          return false;
        }
      }

      // Request media library permissions for gallery access
      const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!mediaPermission.granted) {
        Alert.alert(
          'Photo Library Permission Required',
          'Please allow photo library access to select images.',
          [{ text: 'OK' }]
        );
        return false;
      }

      return true;
    } catch (error) {
      console.error('Permission request error:', error);
      Alert.alert(
        'Permission Error',
        'Failed to request permissions. Please try again.',
        [{ text: 'OK' }]
      );
      return false;
    }
  };

  const handleTakePhoto = async () => {
    try {
      setIsLoading(true);

      const hasPermissions = await requestPermissions();
      if (!hasPermissions) {
        setIsLoading(false);
        return;
      }

      // Navigate to custom camera interface with positioning guide
      // Use replace to avoid navigation stack issues
      const { log } = await import('@/utils/logger');
      log.userAction('Navigate to camera screen', 'UploadScreen');
      router.replace({
        pathname: '/camera',
        params: { source: 'camera' }
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to access camera. Please try again.');
      const { log } = await import('@/utils/logger');
      log.error('Camera navigation error', 'UploadScreen', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectFromGallery = async () => {
    try {
      setIsLoading(true);

      const hasPermissions = await requestPermissions();
      if (!hasPermissions) {
        setIsLoading(false);
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        // Navigate to processing screen with image
        router.push({
          pathname: '/processing',
          params: { imageUri: result.assets[0].uri }
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select image. Please try again.');
      const { log } = await import('@/utils/logger');
      log.error('Gallery error', 'UploadScreen', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.push('/');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <Button
          title=""
          onPress={handleGoBack}
          variant="ghost"
          size="small"
          icon="chevron.left"
          style={styles.backButton}
        />
        <ThemedText variant="h3">Measure Your Foot</ThemedText>
        <Button
          title=""
          onPress={() => router.push('/(tabs)/tutorial')}
          variant="ghost"
          size="small"
          icon="questionmark.circle"
          style={styles.helpButton}
        />
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Quick Guide */}
        <ThemedView style={styles.section}>
          <Card variant="outlined" padding="large">
            <CardContent>
              <View style={styles.quickGuideHeader}>
                <IconSymbol
                  size={32}
                  name="camera.fill"
                  color={colors.primary}
                />
                <ThemedText variant="h4">Ready to measure?</ThemedText>
              </View>

              <ThemedText variant="body" color="secondary" style={styles.quickGuideText}>
                Take a clear photo of your foot from above on a flat surface.
                Need detailed guidance? Tap the help button above.
              </ThemedText>
            </CardContent>
          </Card>
        </ThemedView>

        {/* Upload Options */}
        <ThemedView style={styles.section}>
          <ThemedText variant="h4" style={styles.sectionTitle}>
            Choose an option
          </ThemedText>
          
          <View style={styles.uploadOptions}>
            {/* Take Photo Option */}
            <Card variant="outlined" padding="large" onPress={handleTakePhoto} disabled={isLoading}>
              <CardContent>
                <View style={styles.optionContent}>
                  <View style={[styles.optionIcon, { backgroundColor: colors.primary + '20' }]}>
                    <IconSymbol
                      size={32}
                      name="camera.fill"
                      color={colors.primary}
                    />
                  </View>
                  <ThemedText variant="h4">Take Photo</ThemedText>
                  <ThemedText variant="body" color="secondary" style={styles.optionDescription}>
                    Use your camera to capture a new photo of your foot
                  </ThemedText>
                </View>
              </CardContent>
            </Card>

            {/* Select from Gallery Option */}
            <Card variant="outlined" padding="large" onPress={handleSelectFromGallery} disabled={isLoading}>
              <CardContent>
                <View style={styles.optionContent}>
                  <View style={[styles.optionIcon, { backgroundColor: colors.primary + '20' }]}>
                    <IconSymbol
                      size={32}
                      name="photo.fill"
                      color={colors.primary}
                    />
                  </View>
                  <ThemedText variant="h4">Choose from Gallery</ThemedText>
                  <ThemedText variant="body" color="secondary" style={styles.optionDescription}>
                    Select an existing photo from your photo library
                  </ThemedText>
                </View>
              </CardContent>
            </Card>
          </View>
        </ThemedView>


      </ScrollView>
    </SafeAreaView>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
  },
  helpButton: {
    width: 40,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  quickGuideHeader: {
    alignItems: 'center',
    gap: 12,
    marginBottom: 16,
  },
  quickGuideText: {
    textAlign: 'center',
    lineHeight: 20,
  },
  uploadOptions: {
    gap: 16,
  },
  optionContent: {
    alignItems: 'center',
    gap: 12,
  },
  optionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionDescription: {
    textAlign: 'center',
    maxWidth: 240,
  },

});
