import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import { router, useFocusEffect } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Alert,
  BackHandler,
  Dimensions,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useTheme } from '@/contexts/ThemeContext';

// Camera tab with direct camera functionality and stability improvements
export default function CameraTab() {
  const { colors } = useTheme();
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isCapturing, setIsCapturing] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const cameraRef = useRef<CameraView>(null);

  // Calculate screen dimensions for full-width viewfinder
  const screenWidth = Dimensions.get('window').width;
  const viewfinderSize = screenWidth - 40; // 20px padding on each side

  // Import required hooks and modules - moved to top-level imports for stability

  // Handle back button and cleanup with stable callback
  const handleFocusEffect = useCallback(() => {
    try {
      // Hide status bar for full-screen experience
      StatusBar.setHidden(true);
    } catch (error) {
      console.warn('StatusBar.setHidden error:', error);
    }

    // Handle hardware back button
    const onBackPress = () => {
      router.push('/(tabs)'); // Navigate to home tab
      return true;
    };

    const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

    return () => {
      try {
        // Cleanup: Show status bar when leaving
        StatusBar.setHidden(false);
      } catch (error) {
        console.warn('StatusBar cleanup error:', error);
      }
      subscription?.remove();
    };
  }, []);

  useFocusEffect(handleFocusEffect);

  // Initialize component safely
  useEffect(() => {
    const initializeCamera = async () => {
      try {
        // Small delay to ensure component is fully mounted
        await new Promise(resolve => setTimeout(resolve, 100));
        setIsInitialized(true);
      } catch (error) {
        console.warn('Camera initialization error:', error);
        setIsInitialized(true); // Still allow component to render
      }
    };

    initializeCamera();
  }, []);

  // Cleanup camera resources on unmount
  useEffect(() => {
    return () => {
      // Cleanup camera resources
      try {
        // Reset camera state
        setIsCapturing(false);
        setIsCameraReady(false);
      } catch (error) {
        // Silent cleanup - don't use async operations in cleanup
        console.warn('Camera cleanup error:', error);
      }
    };
  }, []);

  // Handle navigation to home
  const handleClose = async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      router.push('/(tabs)'); // Navigate to home tab
    } catch (error) {
      const logError = async () => {
        const { log } = await import('@/utils/logger');
        log.error('Navigation error', 'CameraScreen', error);
      };
      logError();
      router.replace('/(tabs)');
    }
  };

  // Handle camera permission and initialization
  if (!permission || !isInitialized) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText variant="body">Loading camera...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.permissionContainer}>
          <IconSymbol size={64} name="camera" color={colors.text} style={styles.permissionIcon} />
          <ThemedText variant="h3" style={styles.permissionTitle}>
            Camera Access Required
          </ThemedText>
          <ThemedText variant="body" style={styles.permissionMessage}>
            FootFit needs camera access to capture photos of your feet for accurate measurements.
          </ThemedText>
          <Button
            title="Grant Camera Permission"
            onPress={requestPermission}
            variant="primary"
            style={styles.permissionButton}
          />
          <Button
            title="Go to Upload"
            onPress={() => router.push('/upload')}
            variant="outline"
            style={styles.backButton}
          />
        </ThemedView>
      </SafeAreaView>
    );
  }

  // Handle photo capture with improved stability
  const takePicture = async () => {
    if (!cameraRef.current || isCapturing || !isCameraReady) {
      const { log } = await import('@/utils/logger');
      log.debug('Camera not ready for capture', 'CameraScreen', {
        hasRef: !!cameraRef.current,
        isCapturing,
        isCameraReady
      });
      return;
    }

    try {
      setIsCapturing(true);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Add timeout to prevent hanging
      const capturePromise = cameraRef.current.takePictureAsync({
        quality: 0.7, // Reduced quality for better performance
        base64: false,
        skipProcessing: true, // Skip processing for better performance
      });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Camera capture timeout')), 10000)
      );

      const photo = await Promise.race([capturePromise, timeoutPromise]) as any;

      if (photo?.uri) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Navigate to processing with photo URI
        router.push({
          pathname: '/processing',
          params: { imageUri: photo.uri }
        });
      } else {
        throw new Error('Failed to capture photo - no URI returned');
      }
    } catch (error: any) {
      const { log } = await import('@/utils/logger');
      log.error('Photo capture error', 'CameraScreen', error);

      // More specific error handling
      let errorMessage = 'Failed to take photo. Please try again.';
      if (error?.message?.includes('timeout')) {
        errorMessage = 'Camera capture timed out. Please try again.';
      } else if (error?.message?.includes('permission')) {
        errorMessage = 'Camera permission denied. Please check settings.';
      }

      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Camera Error', errorMessage, [{ text: 'OK' }]);
    } finally {
      setIsCapturing(false);
    }
  };

  // Toggle camera facing with stability checks
  const toggleCameraFacing = async () => {
    try {
      if (!isCapturing && isCameraReady) {
        setFacing(current => (current === 'back' ? 'front' : 'back'));
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      const logError = async () => {
        const { log } = await import('@/utils/logger');
        log.error('Camera flip error', 'CameraScreen', error);
      };
      logError();
    }
  };

  // Handle gallery selection with improved error handling
  const handleSelectFromGallery = async () => {
    if (isCapturing) return; // Prevent multiple operations

    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Import ImagePicker dynamically to avoid import issues
      const ImagePicker = await import('expo-image-picker');

      // Check permissions first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant photo library access to select images.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Navigate to processing screen with image
        router.push({
          pathname: '/processing',
          params: { imageUri: result.assets[0].uri }
        });
      }
    } catch (error) {
      try {
        const { log } = await import('@/utils/logger');
        log.error('Gallery selection error', 'CameraScreen', error);
      } catch (logError) {
        console.error('Gallery selection error:', error);
      }

      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Gallery Error',
        'Failed to select image. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      {/* Full-screen Camera View */}
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={facing}
        mode="picture"
        onCameraReady={async () => {
          try {
            const { log } = await import('@/utils/logger');
            log.debug('Camera ready', 'CameraScreen');
            setIsCameraReady(true);
          } catch (error) {
            console.warn('Camera ready callback error:', error);
            setIsCameraReady(true); // Still mark as ready to prevent blocking
          }
        }}
        onMountError={async (error) => {
          const { log } = await import('@/utils/logger');
          log.error('Camera mount error', 'CameraScreen', error);
          setIsCameraReady(false);
          Alert.alert(
            'Camera Error',
            'Failed to initialize camera. Please restart the app.',
            [
              { text: 'Go to Home', onPress: handleClose }
            ]
          );
        }}
      />

      {/* Header Controls with Close Button */}
      <View style={[styles.header, { backgroundColor: 'rgba(0, 0, 0, 0.3)' }]}>
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
          onPress={handleClose}
        >
          <IconSymbol size={24} name="xmark" color="#fff" />
        </TouchableOpacity>

        <ThemedText variant="labelLarge" style={styles.headerTitle}>
          Take Foot Photo
        </ThemedText>

        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
          onPress={toggleCameraFacing}
          disabled={!isCameraReady}
        >
          <IconSymbol size={24} name="camera.rotate" color={isCameraReady ? "#fff" : "#666"} />
        </TouchableOpacity>
      </View>

      {/* Square Viewfinder Frame */}
      <View style={styles.viewfinderContainer}>
        <View style={[styles.viewfinderFrame, { width: viewfinderSize, height: viewfinderSize }]}>
          <View style={[styles.viewfinderCorner, styles.topLeft]} />
          <View style={[styles.viewfinderCorner, styles.topRight]} />
          <View style={[styles.viewfinderCorner, styles.bottomLeft]} />
          <View style={[styles.viewfinderCorner, styles.bottomRight]} />
        </View>

        {/* Instructions inside viewfinder */}
        <View style={styles.viewfinderInstructions}>
          <ThemedText variant="body" style={styles.instructionText}>
            Position your foot in the square
          </ThemedText>
          <ThemedText variant="caption" style={styles.instructionSubtext}>
            Side view (barefoot) works best
          </ThemedText>
          <ThemedText variant="caption" style={styles.instructionSubtext}>
            Top view and socks also supported
          </ThemedText>
        </View>
      </View>

      {/* Semi-transparent overlay with square cutout */}
      <View style={styles.cameraOverlay}>
        <View style={styles.overlaySection} />
        <View style={[styles.middleRow, { height: viewfinderSize }]}>
          <View style={styles.overlaySection} />
          <View style={[styles.squareCutout, { width: viewfinderSize, height: viewfinderSize }]} />
          <View style={styles.overlaySection} />
        </View>
        <View style={styles.overlaySection} />
      </View>

      {/* Bottom Controls */}
      <View style={styles.controls}>
        <View style={styles.controlsBackground}>
          {/* Gallery button */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.backgroundSecondary }]}
            onPress={handleSelectFromGallery}
          >
            <IconSymbol size={24} name="photo.on.rectangle" color={colors.text} />
          </TouchableOpacity>

          {/* Capture button */}
          <TouchableOpacity
            style={[
              styles.captureButton,
              {
                borderColor: colors.primary,
                backgroundColor: isCapturing ? colors.primary : 'transparent'
              }
            ]}
            onPress={takePicture}
            disabled={isCapturing}
          >
            <View style={[
              styles.captureButtonInner,
              {
                backgroundColor: isCapturing ? colors.textInverse : colors.primary,
                transform: [{ scale: isCapturing ? 0.8 : 1 }]
              }
            ]} />
          </TouchableOpacity>

          {/* Help button */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.backgroundSecondary }]}
            onPress={() => router.push('/(tabs)/tutorial')}
          >
            <IconSymbol size={24} name="questionmark.circle" color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Ensure full-screen overlay
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    gap: 20,
  },
  permissionIcon: {
    marginBottom: 20,
  },
  permissionTitle: {
    textAlign: 'center',
    marginBottom: 10,
  },
  permissionMessage: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  permissionButton: {
    width: '100%',
    marginBottom: 10,
  },
  backButton: {
    width: '100%',
  },
  camera: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: 60,
    zIndex: 1,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  viewfinderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  viewfinderFrame: {
    position: 'relative',
  },
  viewfinderCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#fff',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  viewfinderInstructions: {
    position: 'absolute',
    bottom: -60,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  overlaySection: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  middleRow: {
    flexDirection: 'row',
  },
  squareCutout: {
    backgroundColor: 'transparent',
  },
  instructionText: {
    color: '#fff',
    textAlign: 'center',
    marginBottom: 4,
  },
  instructionSubtext: {
    color: '#fff',
    textAlign: 'center',
    opacity: 0.8,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 40,
    paddingHorizontal: 20,
    zIndex: 1,
  },
  controlsBackground: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
});
