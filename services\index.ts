/**
 * FootFit Services Index
 * 
 * Consolidated exports for all FootFit services
 * Provides clean imports and clear service organization
 */

// Main AI Analysis Service
export { default as FootAnalysisAI } from './footAnalysisAI';

// Database Services
export { SupabaseService } from './supabaseService';
export {
  ShoeCategoryService,
  ShoeBrandService,
  ShoeModelService,
  UserPreferencesService,
  ShoeRecommendationService,
  getShoeBrands,
  getShoeCategories,
} from './shoeDataService';

// Type Definitions
export type {
  FootMeasurement,
  MeasurementRequest,
  MeasurementResponse,
  ShoeRecommendation,
} from './types';

export type {
  ShoeCategory,
  ShoeBrand,
  ShoeModel,
  ShoeSize,
  UserShoePreferences,
  RecommendationRequest,
} from './shoeDataService';

// Legacy exports for backward compatibility
export type {
  FootMeasurement as LegacyFootMeasurement,
  ShoeRecommendation as LegacyShoeRecommendation,
  MeasurementRequest as LegacyMeasurementRequest,
  MeasurementResponse as LegacyMeasurementResponse,
} from './mockAI';
