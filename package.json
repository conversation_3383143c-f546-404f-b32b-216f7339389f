{"name": "footfitappv3", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "verify": "node ./scripts/comprehensive_verification.js", "test-e2e": "node ./scripts/test_end_to_end.js", "validate-dataset": "node ./scripts/validate_dataset.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.50.3", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-react-native": "^1.0.0", "dotenv": "^17.2.0", "expo": "^53.0.19", "expo-blur": "~14.1.5", "expo-camera": "^16.1.10", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-gl": "^15.1.7", "expo-gl-cpp": "^11.4.0", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "promise": "^8.3.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "resolutions": {"promise": "^8.3.0"}, "private": true}