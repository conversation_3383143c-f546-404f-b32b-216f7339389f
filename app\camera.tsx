import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import {
  Alert,
  Dimensions,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useTheme } from '@/contexts/ThemeContext';

// Simple, reliable camera component for academic demonstrations
export default function CameraScreen() {
  const { colors } = useTheme();
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isCapturing, setIsCapturing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const cameraRef = useRef<CameraView>(null);

  // Calculate screen dimensions for full-width viewfinder
  const screenWidth = Dimensions.get('window').width;
  const viewfinderSize = screenWidth; // Full width, edge-to-edge

  // Initialize component safely with comprehensive error handling
  useEffect(() => {
    const initializeCamera = async () => {
      try {
        console.log('🔍 [UPLOAD CAMERA] Starting camera initialization...');

        // Check if we're in a valid environment
        if (typeof window === 'undefined') {
          console.warn('⚠️ [UPLOAD CAMERA] Window not available, skipping initialization');
          setIsInitialized(true);
          return;
        }

        // Small delay to ensure component is fully mounted and avoid race conditions
        await new Promise(resolve => setTimeout(resolve, 150));

        console.log('🔍 [UPLOAD CAMERA] Camera initialization completed successfully');
        setIsInitialized(true);
      } catch (error) {
        console.error('❌ [UPLOAD CAMERA] Camera initialization error:', error);
        // Always set initialized to true to prevent infinite loading
        setIsInitialized(true);
      }
    };

    // Use a timeout to prevent hanging initialization
    const initTimeout = setTimeout(() => {
      console.warn('⚠️ [UPLOAD CAMERA] Camera initialization timeout, forcing initialization');
      setIsInitialized(true);
    }, 3000);

    initializeCamera().finally(() => {
      clearTimeout(initTimeout);
    });

    // Cleanup function
    return () => {
      clearTimeout(initTimeout);
    };
  }, []);

  // Handle camera permission and initialization
  if (!permission || !isInitialized) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText variant="body">Loading camera...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.permissionContainer}>
          <IconSymbol size={64} name="camera" color={colors.text} style={styles.permissionIcon} />
          <ThemedText variant="h3" style={styles.permissionTitle}>
            Camera Access Required
          </ThemedText>
          <ThemedText variant="body" style={styles.permissionMessage}>
            FootFit needs camera access to capture photos of your feet for accurate measurements.
          </ThemedText>
          <Button
            title="Grant Camera Permission"
            onPress={requestPermission}
            variant="primary"
            style={styles.permissionButton}
          />
          <Button
            title="Go Back"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </ThemedView>
      </SafeAreaView>
    );
  }

  // Handle photo capture with comprehensive error handling
  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) {
      console.warn('⚠️ [UPLOAD CAMERA] Camera not ready or already capturing');
      return;
    }

    try {
      console.log('🔍 [UPLOAD CAMERA] Starting photo capture...');
      setIsCapturing(true);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        console.log('✅ [UPLOAD CAMERA] Photo captured successfully:', photo.uri);
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Navigate to processing with photo URI
        router.push({
          pathname: '/processing',
          params: { imageUri: photo.uri }
        });
      } else {
        throw new Error('Photo capture returned no URI');
      }
    } catch (error) {
      console.error('❌ [UPLOAD CAMERA] Photo capture error:', error);

      // Log error asynchronously to avoid blocking UI
      setTimeout(async () => {
        try {
          const { log } = await import('@/utils/logger');
          log.error('Photo capture error', 'UploadCameraScreen', error);
        } catch (logError) {
          console.error('Logger error:', logError);
        }
      }, 0);

      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Camera Error',
        'Failed to take photo. Please check camera permissions and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => takePicture() }
        ]
      );
    } finally {
      setIsCapturing(false);
    }
  };

  // Toggle camera facing
  const toggleCameraFacing = async () => {
    try {
      setFacing(current => (current === 'back' ? 'front' : 'back'));
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.warn('Camera flip haptic error:', error);
    }
  };

  // Go back to upload page
  const handleGoBack = async () => {
    try {
      console.log('🔍 [UPLOAD CAMERA] Back button pressed - navigating to upload page');
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      console.log('🔍 [UPLOAD CAMERA] Haptic feedback completed, navigating...');

      // Use push instead of back to ensure we go to upload page
      // This fixes the issue where router.back() goes to home instead of upload
      router.push('/upload');
      console.log('🔍 [UPLOAD CAMERA] Navigation to upload page sent');
    } catch (error) {
      console.error('❌ [UPLOAD CAMERA] Navigation error:', error);
      const logError = async () => {
        const { log } = await import('@/utils/logger');
        log.error('Upload flow navigation error', 'UploadCameraScreen', error);
      };
      logError();
      // Fallback navigation
      router.push('/upload');
    }
  };

  // Handle gallery selection with comprehensive error handling
  const handleSelectFromGallery = async () => {
    if (isCapturing) {
      console.warn('⚠️ [UPLOAD CAMERA] Gallery selection blocked - camera is capturing');
      return;
    }

    try {
      console.log('🔍 [UPLOAD CAMERA] Starting gallery selection...');
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Import ImagePicker dynamically to avoid import issues
      const ImagePicker = await import('expo-image-picker');

      // Check permissions first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        console.warn('⚠️ [UPLOAD CAMERA] Photo library permission denied');
        Alert.alert(
          'Permission Required',
          'Please grant photo library access to select images.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        console.log('✅ [UPLOAD CAMERA] Image selected from gallery:', result.assets[0].uri);
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Navigate to processing screen with image
        router.push({
          pathname: '/processing',
          params: { imageUri: result.assets[0].uri }
        });
      } else {
        console.log('🔍 [UPLOAD CAMERA] Gallery selection cancelled by user');
      }
    } catch (error) {
      console.error('❌ [UPLOAD CAMERA] Gallery selection error:', error);

      // Log error asynchronously to avoid blocking UI
      setTimeout(async () => {
        try {
          const { log } = await import('@/utils/logger');
          log.error('Gallery selection error', 'UploadCameraScreen', error);
        } catch (logError) {
          console.error('Logger error:', logError);
        }
      }, 0);

      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Gallery Error',
        'Failed to select image. Please check photo library permissions and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => handleSelectFromGallery() }
        ]
      );
    }
  };

  return (
    <View style={styles.container}>
      {/* Full-screen Camera View */}
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={facing}
        mode="picture"
        onCameraReady={() => {
          console.log('🔍 [UPLOAD CAMERA] Camera ready and mounted successfully');
          // Avoid heavy async operations in camera callbacks to prevent crashes
        }}
        onMountError={(error) => {
          console.error('❌ [UPLOAD CAMERA] Camera mount error:', error);
          // Log error asynchronously without blocking the callback
          setTimeout(async () => {
            try {
              const { log } = await import('@/utils/logger');
              log.error('Camera mount error', 'UploadCameraScreen', error);
            } catch (logError) {
              console.error('Logger error:', logError);
            }
          }, 0);

          // Show user-friendly error message
          Alert.alert(
            'Camera Error',
            'Failed to initialize camera. Please check permissions and try again.',
            [
              { text: 'Go Back', onPress: () => router.push('/upload') },
              { text: 'Retry', onPress: () => router.replace('/camera') }
            ]
          );
        }}
      />

      {/* Header Controls */}
      <View style={[styles.header, { backgroundColor: 'rgba(0, 0, 0, 0.4)' }]}>
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: 'rgba(0, 0, 0, 0.6)' }]}
          onPress={handleGoBack}
          onPressIn={() => console.log('🔍 [UPLOAD CAMERA] Back button touch started')}
          onPressOut={() => console.log('🔍 [UPLOAD CAMERA] Back button touch ended')}
          activeOpacity={0.7}
        >
          <IconSymbol size={24} name="chevron.left" color="#fff" />
        </TouchableOpacity>

        <ThemedText variant="labelLarge" style={styles.headerTitle}>
          Take Foot Photo
        </ThemedText>

        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: 'rgba(0, 0, 0, 0.6)' }]}
          onPress={() => {
            console.log('🔍 [UPLOAD CAMERA] Tutorial button pressed');
            router.push('/(tabs)/tutorial');
          }}
          onPressIn={() => console.log('🔍 [UPLOAD CAMERA] Tutorial button touch started')}
          onPressOut={() => console.log('🔍 [UPLOAD CAMERA] Tutorial button touch ended')}
          activeOpacity={0.7}
        >
          <IconSymbol size={24} name="questionmark.circle" color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Square Viewfinder Frame */}
      <View style={styles.viewfinderContainer}>
        <View style={[styles.viewfinderFrame, { width: viewfinderSize, height: viewfinderSize }]}>
          <View style={[styles.viewfinderCorner, styles.topLeft]} />
          <View style={[styles.viewfinderCorner, styles.topRight]} />
          <View style={[styles.viewfinderCorner, styles.bottomLeft]} />
          <View style={[styles.viewfinderCorner, styles.bottomRight]} />
        </View>

        {/* Instructions inside viewfinder */}
        <View style={styles.viewfinderInstructions}>
          <ThemedText variant="body" style={styles.instructionText}>
            Position your foot in the square
          </ThemedText>
          <ThemedText variant="caption" style={styles.instructionSubtext}>
            Make sure your entire foot is visible
          </ThemedText>
        </View>
      </View>

      {/* Semi-transparent overlay with full-width square cutout */}
      <View style={styles.cameraOverlay}>
        <View style={styles.overlaySection} />
        <View style={[styles.squareCutout, { width: viewfinderSize, height: viewfinderSize }]} />
        <View style={styles.overlaySection} />
      </View>

      {/* Bottom Controls */}
      <View style={[styles.controls, { backgroundColor: 'rgba(0, 0, 0, 0.4)' }]}>
        <View style={styles.controlsBackground}>
          {/* Gallery button */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: 'rgba(0, 0, 0, 0.6)' }]}
            onPress={handleSelectFromGallery}
          >
            <IconSymbol size={24} name="photo.on.rectangle" color="#fff" />
          </TouchableOpacity>

          {/* Capture button */}
          <TouchableOpacity
            style={[
              styles.captureButton,
              {
                borderColor: '#00C851',
                backgroundColor: isCapturing ? '#00C851' : 'transparent'
              }
            ]}
            onPress={takePicture}
            disabled={isCapturing}
          >
            <View style={[
              styles.captureButtonInner,
              {
                backgroundColor: isCapturing ? '#fff' : '#00C851',
                transform: [{ scale: isCapturing ? 0.8 : 1 }]
              }
            ]} />
          </TouchableOpacity>

          {/* Flip camera button */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: 'rgba(0, 0, 0, 0.6)' }]}
            onPress={toggleCameraFacing}
          >
            <IconSymbol size={24} name="camera.rotate" color="#fff" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionIcon: {
    marginBottom: 20,
  },
  permissionTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  permissionMessage: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  permissionButton: {
    width: '100%',
    marginBottom: 12,
  },
  backButton: {
    width: '100%',
  },
  camera: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: 60,
    zIndex: 10,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  viewfinderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  viewfinderFrame: {
    position: 'relative',
  },
  viewfinderCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: '#fff',
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  viewfinderInstructions: {
    position: 'absolute',
    bottom: -60,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  overlaySection: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  squareCutout: {
    backgroundColor: 'transparent',
  },

  instructionText: {
    color: '#fff',
    textAlign: 'center',
    marginBottom: 4,
  },
  instructionSubtext: {
    color: '#fff',
    textAlign: 'center',
    opacity: 0.8,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 40,
    paddingHorizontal: 20,
    zIndex: 10,
  },
  controlsBackground: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
});
