import { CameraType, CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Dimensions,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useTheme } from '@/contexts/ThemeContext';

// Simple, reliable camera component for academic demonstrations
export default function CameraScreen() {
  const { colors } = useTheme();
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isCapturing, setIsCapturing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const cameraRef = useRef<CameraView>(null);

  // Calculate screen dimensions for full-width viewfinder
  const screenWidth = Dimensions.get('window').width;
  const viewfinderSize = screenWidth - 40; // 20px padding on each side

  // Initialize component safely
  useEffect(() => {
    const initializeCamera = async () => {
      try {
        // Small delay to ensure component is fully mounted
        await new Promise(resolve => setTimeout(resolve, 100));
        setIsInitialized(true);
      } catch (error) {
        console.warn('Camera initialization error:', error);
        setIsInitialized(true); // Still allow component to render
      }
    };

    initializeCamera();
  }, []);

  // Handle camera permission and initialization
  if (!permission || !isInitialized) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText variant="body">Loading camera...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.permissionContainer}>
          <IconSymbol size={64} name="camera" color={colors.text} style={styles.permissionIcon} />
          <ThemedText variant="h3" style={styles.permissionTitle}>
            Camera Access Required
          </ThemedText>
          <ThemedText variant="body" style={styles.permissionMessage}>
            FootFit needs camera access to capture photos of your feet for accurate measurements.
          </ThemedText>
          <Button
            title="Grant Camera Permission"
            onPress={requestPermission}
            variant="primary"
            style={styles.permissionButton}
          />
          <Button
            title="Go Back"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </ThemedView>
      </SafeAreaView>
    );
  }

  // Handle photo capture
  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    try {
      setIsCapturing(true);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        // Navigate to processing with photo URI
        router.push({
          pathname: '/processing',
          params: { imageUri: photo.uri }
        });
      } else {
        throw new Error('Failed to capture photo');
      }
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Photo capture error', 'CameraScreen', error);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Camera Error',
        'Failed to take photo. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsCapturing(false);
    }
  };

  // Toggle camera facing
  const toggleCameraFacing = async () => {
    try {
      setFacing(current => (current === 'back' ? 'front' : 'back'));
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.warn('Camera flip haptic error:', error);
    }
  };

  // Go back to home
  const handleGoBack = async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      router.back();
    } catch (error) {
      console.warn('Navigation haptic error:', error);
      router.back();
    }
  };

  // Handle gallery selection with improved error handling
  const handleSelectFromGallery = async () => {
    if (isCapturing) return; // Prevent multiple operations

    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Import ImagePicker dynamically to avoid import issues
      const ImagePicker = await import('expo-image-picker');

      // Check permissions first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant photo library access to select images.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Navigate to processing screen with image
        router.push({
          pathname: '/processing',
          params: { imageUri: result.assets[0].uri }
        });
      }
    } catch (error) {
      try {
        const { log } = await import('@/utils/logger');
        log.error('Gallery selection error', 'CameraScreen', error);
      } catch (logError) {
        console.error('Gallery selection error:', error);
      }

      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Gallery Error',
        'Failed to select image. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      {/* Full-screen Camera View */}
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={facing}
        mode="picture"
        onCameraReady={async () => {
          try {
            const { log } = await import('@/utils/logger');
            log.debug('Camera ready', 'CameraScreen');
          } catch (error) {
            console.warn('Camera ready callback error:', error);
          }
        }}
        onMountError={async (error) => {
          try {
            const { log } = await import('@/utils/logger');
            log.error('Camera mount error', 'CameraScreen', error);
          } catch (logError) {
            console.error('Camera mount error:', error);
          }
          Alert.alert('Camera Error', 'Failed to initialize camera');
        }}
      />

      {/* Header Controls */}
      <View style={[styles.header, { backgroundColor: 'rgba(0, 0, 0, 0.3)' }]}>
        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
          onPress={handleGoBack}
        >
          <IconSymbol size={24} name="chevron.left" color="#fff" />
        </TouchableOpacity>

        <ThemedText variant="labelLarge" style={styles.headerTitle}>
          Take Foot Photo
        </ThemedText>

        <TouchableOpacity
          style={[styles.headerButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
          onPress={toggleCameraFacing}
        >
          <IconSymbol size={24} name="camera.rotate" color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Instructions Overlay */}
      <View style={[styles.instructionsOverlay, { backgroundColor: colors.backgroundSecondary + 'CC' }]}>
        <ThemedText variant="body" style={styles.instructionText}>
          Position your foot in the center of the frame
        </ThemedText>
        <ThemedText variant="caption" style={styles.instructionSubtext}>
          Make sure your entire foot is visible
        </ThemedText>
      </View>

      {/* Bottom Controls */}
      <View style={styles.controls}>
        <View style={styles.controlsBackground}>
          {/* Gallery button */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.backgroundSecondary }]}
            onPress={handleSelectFromGallery}
          >
            <IconSymbol size={24} name="photo.on.rectangle" color={colors.text} />
          </TouchableOpacity>

          {/* Capture button */}
          <TouchableOpacity
            style={[
              styles.captureButton,
              {
                borderColor: colors.primary,
                backgroundColor: isCapturing ? colors.primary : 'transparent'
              }
            ]}
            onPress={takePicture}
            disabled={isCapturing}
          >
            <View style={[
              styles.captureButtonInner,
              {
                backgroundColor: isCapturing ? colors.textInverse : colors.primary,
                transform: [{ scale: isCapturing ? 0.8 : 1 }]
              }
            ]} />
          </TouchableOpacity>

          {/* Help button */}
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.backgroundSecondary }]}
            onPress={() => router.push('/(tabs)/tutorial')}
          >
            <IconSymbol size={24} name="questionmark.circle" color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionIcon: {
    marginBottom: 20,
  },
  permissionTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  permissionMessage: {
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  permissionButton: {
    width: '100%',
    marginBottom: 12,
  },
  backButton: {
    width: '100%',
  },
  camera: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: 60,
    zIndex: 1,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  instructionsOverlay: {
    position: 'absolute',
    top: 120,
    left: 20,
    right: 20,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
    zIndex: 1,
  },
  instructionText: {
    color: '#fff',
    textAlign: 'center',
    marginBottom: 4,
  },
  instructionSubtext: {
    color: '#fff',
    textAlign: 'center',
    opacity: 0.8,
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 40,
    paddingHorizontal: 20,
    zIndex: 1,
  },
  controlsBackground: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
});
