/**
 * FootFit AI Service Types (Legacy)
 *
 * This file is kept for backward compatibility.
 * New code should import types from './types.ts'
 *
 * @deprecated Use './types.ts' instead
 */

// Re-export types from the consolidated types file
export type {
  FootMeasurement, MeasurementRequest,
  MeasurementResponse, ShoeRecommendation
} from './types';

// Import types for internal use
import type {
  FootMeasurement,
  MeasurementRequest,
  MeasurementResponse,
  ShoeRecommendation,
} from './types';

// Mock shoe database
const MOCK_SHOES: ShoeRecommendation[] = [
  {
    brand: 'Nike',
    model: 'Air Jordan 1 Low',
    size_uk: '9.0',
    size_us: '10.0',
    size_eu: '44.0',
    confidence: 0.95,
    fit_type: 'regular',
    category: 'Basketball',
    image_url: 'https://via.placeholder.com/200x200/00C851/FFFFFF?text=Nike+AJ1',
  },
  {
    brand: 'Adidas',
    model: '<PERSON>',
    size_uk: '9.0',
    size_us: '10.0',
    size_eu: '44.0',
    confidence: 0.92,
    fit_type: 'regular',
    category: 'Lifestyle',
    image_url: 'https://via.placeholder.com/200x200/00C851/FFFFFF?text=Adidas+Stan',
  },
  {
    brand: 'Converse',
    model: 'Chuck Taylor All Star',
    size_uk: '8.5',
    size_us: '9.5',
    size_eu: '43.0',
    confidence: 0.88,
    fit_type: 'narrow',
    category: 'Lifestyle',
    image_url: 'https://via.placeholder.com/200x200/00C851/FFFFFF?text=Converse+CT',
  },
  {
    brand: 'Vans',
    model: 'Old Skool',
    size_uk: '9.0',
    size_us: '10.0',
    size_eu: '44.0',
    confidence: 0.90,
    fit_type: 'regular',
    category: 'Skate',
    image_url: 'https://via.placeholder.com/200x200/00C851/FFFFFF?text=Vans+OS',
  },
  {
    brand: 'New Balance',
    model: '990v5',
    size_uk: '9.5',
    size_us: '10.5',
    size_eu: '44.5',
    confidence: 0.87,
    fit_type: 'wide',
    category: 'Running',
    image_url: 'https://via.placeholder.com/200x200/00C851/FFFFFF?text=NB+990',
  },
];

// Simulate processing delay
const PROCESSING_DELAY_MS = 2000; // 2 seconds

// Generate realistic foot measurements based on image analysis simulation
function generateMockMeasurement(imageUrl: string, preferences?: MeasurementRequest['user_preferences']): FootMeasurement {
  // Simulate some variation in measurements
  const baseLength = 25.5 + Math.random() * 3; // 25.5-28.5 cm
  const baseWidth = 9.0 + Math.random() * 2; // 9.0-11.0 cm
  
  // Calculate UK size based on length (simplified formula)
  const ukSize = Math.round((baseLength - 23) * 1.5 * 2) / 2; // Half sizes
  const usSize = ukSize + 1;
  const euSize = ukSize + 33;

  // Filter recommendations based on preferences
  let filteredShoes = [...MOCK_SHOES];
  
  if (preferences?.preferred_brands?.length) {
    filteredShoes = filteredShoes.filter(shoe => 
      preferences.preferred_brands!.some(brand => 
        shoe.brand.toLowerCase().includes(brand.toLowerCase())
      )
    );
  }

  if (preferences?.preferred_fit) {
    filteredShoes = filteredShoes.filter(shoe => shoe.fit_type === preferences.preferred_fit);
  }

  // If no shoes match preferences, fall back to all shoes
  if (filteredShoes.length === 0) {
    filteredShoes = [...MOCK_SHOES];
  }

  // Take top 3 recommendations and adjust sizes
  const recommendations = filteredShoes
    .slice(0, 3)
    .map(shoe => ({
      ...shoe,
      size_uk: ukSize.toString(),
      size_us: usSize.toString(),
      size_eu: euSize.toString(),
      confidence: 0.85 + Math.random() * 0.15, // 0.85-1.0
    }));

  return {
    foot_length: Math.round(baseLength * 10) / 10,
    foot_width: Math.round(baseWidth * 10) / 10,
    recommended_size_uk: ukSize.toString(),
    recommended_size_us: usSize.toString(),
    recommended_size_eu: euSize.toString(),
    confidence: 0.90 + Math.random() * 0.10, // 0.90-1.0
    recommendations,
  };
}

// Legacy MockAIService - DEPRECATED
// Use FootAnalysisAI from './footAnalysisAI' instead
export class MockAIService {
  static async measureFoot(request: MeasurementRequest): Promise<MeasurementResponse> {
    const startTime = Date.now();
    const maxRetries = 2;
    const timeoutMs = 15000; // 15 seconds

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Create processing promise with timeout
        const processingPromise = new Promise<FootMeasurement>((resolve, reject) => {
          setTimeout(() => {
            try {
              // Disable random failures for development stability
              // if (Math.random() < 0.001) {
              //   reject(new Error('AI processing failed. Please try again.'));
              //   return;
              // }

              // Generate mock measurement
              const measurement = generateMockMeasurement(request.image_url, request.user_preferences);
              resolve(measurement);
            } catch (error) {
              reject(error);
            }
          }, PROCESSING_DELAY_MS);
        });

        // Create timeout promise
        const timeoutPromise = new Promise<FootMeasurement>((_, reject) => {
          setTimeout(() => {
            reject(new Error('AI processing timeout. Please try again.'));
          }, timeoutMs);
        });

        // Race between processing and timeout
        const measurement = await Promise.race([processingPromise, timeoutPromise]);

        const processingTime = Date.now() - startTime;

        return {
          success: true,
          data: measurement,
          processing_time_ms: processingTime,
        };

      } catch (error) {
        const processingTime = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

        console.error(`AI processing error (attempt ${attempt}):`, errorMessage);

        // Retry on timeout or network errors
        if (attempt < maxRetries && (errorMessage.includes('timeout') || errorMessage.includes('network'))) {
          console.log(`Retrying AI processing in ${attempt * 1000}ms...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }

        // Return error after all retries or non-retryable error
        return {
          success: false,
          error: errorMessage,
          processing_time_ms: processingTime,
        };
      }
    }

    // This should never be reached, but included for completeness
    return {
      success: false,
      error: 'AI processing failed after all retries',
      processing_time_ms: Date.now() - startTime,
    };
  }

  // Simulate image upload to storage
  static async uploadImage(imageUri: string): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate occasional upload failures (2% chance)
      if (Math.random() < 0.02) {
        throw new Error('Image upload failed. Please try again.');
      }

      // Generate mock URL
      const mockUrl = `https://footfit-storage.example.com/uploads/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`;
      
      return {
        success: true,
        url: mockUrl,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  // Get available shoe brands for preferences
  static getAvailableBrands(): string[] {
    return [...new Set(MOCK_SHOES.map(shoe => shoe.brand))];
  }

  // Get available categories
  static getAvailableCategories(): string[] {
    return [...new Set(MOCK_SHOES.map(shoe => shoe.category))];
  }
}
